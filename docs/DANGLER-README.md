# Octopi Brute Force Object Discovery

This enhancement adds TruffleHog-style brute force commit discovery to Octopi.

## 🎯 **Use Cases**

### **When to Use Brute Force Discovery:**
- **Single repository deep analysis** - Comprehensive discovery for specific high-value targets
- **API limitations** - When GitHub Events API doesn't have sufficient history
- **Maximum coverage** - When you need to find every possible dangling commit
- **Research/forensics** - When investigating specific repositories thoroughly

### **When to Use Traditional Dangler:**
- **Bulk scanning** - Scanning many repositories efficiently
- **Regular monitoring** - Ongoing detection of new dangling commits
- **API efficiency** - When you want minimal API usage
- **Speed** - When you need fast results

## 🚀 **Quick Start**

### **Basic Brute Force Scan:**
```bash
# Scan single repository with brute force discovery
octopi scan --dangler --gitleaks --repo owner/repo
```

### **Advanced Configuration:**
```bash
# Customize brute force parameters
octopi scan \
  --dangler \
  --dangler-max-char 6 \
  --dangler-chunk-size 500 \
  --dangler-max-queries 200 \
  --gitleaks \
  --repo owner/high-value-repo
```

## ⚙️ **Configuration Options**

| Flag | Default | Description |
|------|---------|-------------|
| `--dangler` | `false` | Enable brute force discovery |
| `--dangler-max-char` | `5` | Maximum SHA character length (4-6) |
| `--dangler-collision` | `1.0` | Collision threshold for SHA calculation |
| `--dangler-chunk-size` | `350` | Commits per GraphQL query |
| `--dangler-max-queries` | `100` | Maximum GraphQL queries |
| `--dangler-cache` | `""` | Cache directory for results |
| `--dangler-test-commits` | `""` | Test specific commits (comma-separated) for debugging |

## 🚨 **Important Constraints**

- **Mutual Exclusivity**: Bruter and Dangler are mutually exclusive (cannot be used together)
- **Manual Targets Only**: Bruter can only be used with manual targets:
  - `--repos owner/repo`
  - `--org organization`
  - `--user username`
  - `--repos-file file.txt`
- **No Database Mode**: When using bruter, repositories are not loaded from database
- **Caching System**: Uses TruffleHog-style caching with both valid and invalid commits for resuming scans

## 📊 **Performance Characteristics**

### **SHA Length vs Performance:**

| Length | Combinations | Est. Queries | Time | Use Case |
|--------|-------------|--------------|------|----------|
| 4 chars | 65K | ~185 | ~2 min | Small repos |
| 5 chars | 1M | ~2,900 | ~30 min | Medium repos |
| 6 chars | 16M | ~46,000 | ~8 hours | Large repos |

### **Comparison: Dangler vs Bruter**

| Metric | Dangler (API) | Bruter |
|--------|---------------|-------------|
| **Speed** | ⚡ Fast (10-40 API calls) | 🐌 Slow (100-46K queries) |
| **Coverage** | 📊 Good (67-90%) | 🎯 Comprehensive (100%) |
| **API Usage** | 💚 Low | 🔴 High |
| **Best For** | Bulk scanning | Single repo analysis |


## 📈 **Usage Examples**

### **1. Research Mode - Maximum Discovery**
```bash
# Deep analysis of suspicious repository
octopi scan \
  --dangler \
  --dangler-max-char 6 \
  --dangler-max-queries 1000 \
  --gitleaks \
  --repo suspicious-org/leaked-repo
```

### **2. Targeted Analysis - Specific Repository**
```bash
# Focus on single high-value target
octopi scan \
  --dangler \
  --dangler-max-char 5 \
  --gitleaks \
  --repo target-org/secret-project
```

### **3. Test Specific Commits - Debugging Mode**
```bash
# Test known commits for validation
octopi scan \
  --dangler \
  --dangler-test-commits "27dd9cb,f35532,3e587a" \
  --gitleaks \
  --repo hudjefa/hidden-secrets
```

### **4. Cached Analysis - Resume Previous Scan**
```bash
# Use cached results to avoid re-scanning
octopi scan \
  --dangler \
  --dangler-cache /persistent/cache \
  --gitleaks \
  --repo owner/repo
```

## 🧪 **Testing Commands**

### **Test Repository: hudjefa/hidden-secrets**

This repository contains known dangling commits for testing bruter functionality:

#### **Test All Known Valid Commits:**
```bash
# Test all commits from scripts/commit-tracking-github.json
octopi scan \
  --dangler \
  --dangler-test-commits "e90879d2110831470d5ad2fee19c59af5ce30cc2,27dd9cb31b07f86f2920b5a36aa7daa2e4da3bbc,f35532693af8e71c7e12cb8824f78944b6e4c8c7,70dbe61ac924fe21fe4730c5cbc4109dd4437cae,9b5d43a5f0c75248918fc97409643e5dbb16cf3b,73543d93306ec316c54543ebefb948571dc40bba,d1905d83beb1057f0a097fab16991b83ab69b646,a4f5d5bd0ad4e890c93cbb32132a9628369e8ec7,6e71478c712af3723c68c0ff2e6c4bb6bd180649,3e587aae6576bd2764337d21cf82423db0fbbd1d,b4f90db78c520b382976925aa747a30456b49381,9c5b26ba36e41ffd8f1556319948445a7177ff17,346707cb553bfb5ec3335573f7d8b19c0b9634e8,0327382ba9ce6e4626e59ab6776064ce94efbfa3,9276155f56e674e962ac5ebbd2ac7c84e15ee45b,b81f7b6977a69800e457fd51241c30c585d41911,ba60298e820c1c7bdd08b27b9f7f5d0bf9f87412,8bc8f4bae014bd49b60eb764b23a6b59822bbe1a,517b0b6b2525eb1204eda2e50016cd88b6a17485,d56fc7dd362ab3da6254ee995e53c409a2893a59,bf13a13ccdfa8c6e6254c29a20a84c279ec551ab" \
  --gitleaks \
  --repo hudjefa/hidden-secrets
```

#### **Test Only Dangling Commits (Expected to be Hidden):**
```bash
# Test commits that should be dangling/hidden
octopi scan \
  --dangler \
  --dangler-test-commits "27dd9cb31b07f86f2920b5a36aa7daa2e4da3bbc,f35532693af8e71c7e12cb8824f78944b6e4c8c7,346707cb553bfb5ec3335573f7d8b19c0b9634e8,517b0b6b2525eb1204eda2e50016cd88b6a17485" \
  --gitleaks \
  --repo hudjefa/hidden-secrets
```

#### **Test Short SHA Prefixes:**
```bash
# Test with short SHA prefixes (4-6 chars)
octopi scan \
  --dangler \
  --dangler-test-commits "27dd9cb,f35532,3e587a,346707,517b0b" \
  --gitleaks \
  --repo hudjefa/hidden-secrets
```

#### **Full Brute Force Test (4 chars):**
```bash
# Brute force scan with 4 character length (fast test)
octopi scan \
  --dangler \
  --dangler-max-char 4 \
  --dangler-max-queries 200 \
  --gitleaks \
  --repo hudjefa/hidden-secrets
```

### **Expected Results:**
- **Valid commits**: Should return full SHA hashes for existing commits
- **Invalid commits**: Should be logged as invalid/not found
- **Dangling commits**: May or may not be discoverable depending on GitHub's object accessibility
- **Regular commits**: Should always be discoverable and return full SHA

## ⚠️ **Important Considerations**

### **Rate Limiting:**
- GitHub GraphQL API has rate limits
- Brute force makes many requests (100-46K)
- Built-in delays and backoff mechanisms
- Monitor your API quota

### **Time Requirements:**
- 4 chars: ~2 minutes
- 5 chars: ~30 minutes
- 6 chars: ~8 hours
- Plan accordingly for large repositories

### **Resource Usage:**
- High API quota consumption
- Significant network traffic
- CPU intensive for large SHA spaces
- Consider running during off-hours

## 🎯 **Best Practices**

### **1. Start Small:**
```bash
# Test with 4 characters first
octopi scan --dangler --dangler-max-char 4 --repo owner/repo
```

### **2. Use Caching with custom directory:**
```bash
# Specify custom cache directory
octopi scan --dangler --dangler-cache /persistent/cache --repo owner/repo
```

### **3. Monitor Progress:**
- Watch logs for progress updates
- Check API rate limit status
- Use `--debug` for detailed information

### **4. Start with Testing:**
```bash
# Test specific commits first to validate functionality
octopi scan --dangler --dangler-test-commits "abc123,def456" --gitleaks --repo owner/repo
```

### **6. Manual Targets Only:**
```bash
# ✅ Bruter works with manual targets
octopi scan --dangler --repo owner/repo
octopi scan --dangler --org organization
octopi scan --dangler --user username
octopi scan --dangler --repos-file repos.txt

# ❌ Dangler does NOT work with database repos
octopi scan --dangler  # This will fail - no manual targets specified
```

## 🔍 **Output and Results**

### **Enhanced Findings:**
All findings will be marked with as a damgler discovered commit:
```json
{
  "commit": "abc123...",
  "dangler_commit": true,
  "secret": "...",
  "file": "config.json"
}
```

### **Statistics:**
```
Brute Force Discovery Stats:
- Total SHAs tested: 1,048,576
- Valid commits found: 23
- GraphQL queries made: 2,996
- Duration: 28m 34s
- Cache hits: 0
```

## 🚨 **Safety Features**

### **Automatic Limits:**
- Maximum 6 character SHA length
- Maximum 900 commits per GraphQL query
- Warnings for high query estimates
- Graceful error handling

### **Progress Monitoring:**
- Real-time progress updates
- Query count tracking
- Error rate monitoring
- Estimated completion time

## 🔄 **Integration with Existing Workflow**

The bruter discovery integrates seamlessly with existing Octopi workflows:

1. **Discovery Phase:** Bruter finds additional commits
2. **Fetching Phase:** `FetchDanglingCommit()` downloads all commits
3. **Scanning Phase:** Gitleaks/TruffleHog scan all commits
4. **Reporting Phase:** Results marked with discovery method

## 📝 **Implementation Notes**

- **Based on TruffleHog's proven object discovery method**
- **Uses GitHub GraphQL API for efficient batch testing**
- **Implements TruffleHog-style caching** with both valid and invalid commits for resuming scans
- **Includes collision avoidance** using Birthday Paradox calculations
- **Provides comprehensive error handling and recovery**
- **Mutual exclusivity with Dangler** - choose the right tool for your use case
- **Manual targets only** - designed for targeted analysis, not bulk scanning
- **Test mode support** - validate functionality with known commits before full scans

## 🔄 **Current Status**

✅ **Completed Features:**
- Core brute force discovery engine
- TruffleHog-style caching system (valid_hidden.txt + invalid.txt)
- Test commits functionality for debugging
- Proper integration with scan workflow
- Mutual exclusivity validation
- Manual targets validation
- GraphQL batch testing with rate limiting

✅ **Ready for Testing:**
- Use `hudjefa/hidden-secrets` repository for validation
- Test with known commit hashes from `scripts/commit-tracking-github.json`
- Validate both valid and invalid commit detection

This enhancement makes Octopi a comprehensive tool for discovering dangling commits, offering both the speed of API-based discovery (Dangler) and the thoroughness of brute force enumeration (Bruter) as complementary approaches.
