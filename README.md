# Octopi
```
 ▄██████▄   ▄████████     ███      ▄██████▄     ▄███████▄  ▄█
███    ███ ███    ███ ▀█████████� ███    ███   ███    ███ ███
███    ███ ███    █▀     ▀███▀▀██ ███    ███   ███    ███ ███▌
███    ███ ███            ███   ▀ ███    ███   ███    ███ ███▌
███    ███ ███            ███     ███    ███ ▀█████████▀  ███▌
███    ███ ███    █▄      ███     ███    ███   ███        ███
███    ███ ███    ███     ███     ███    ███   ███        ███
 ▀██████▀  ████████▀     ▄████▀    ▀██████▀   ▄████▀      █▀

## Overview

*Octopi* is a powerful GitHub repository collection and secret scanning tool designed for security researchers and penetration testers. It collects GitHub repositories based on code/commit/user search queries or through enumeration of organizations, repositories, users, and emails. All results are saved to a SQLite database and can be automatically scanned for exposed secrets using integrated scanners like Gitleaks and TruffleHog.

## Features

- **Multiple Search Methods**: Search GitHub by code, commits, repositories, users, or emails
- **Enumeration Capabilities**: Enumerate organizations, users, repositories, and emails to discover related repositories
- **Rate Limit Handling**: Smart rate limit handling with exponential backoff and jitter
- **Authentication Support**: GitHub token authentication and web login with 2FA support
- **Filtering Options**: Filter repositories by size, fork status, and more
- **Database Storage**: Store all results in SQLite for persistent access
- **Integrated Secret Scanning**: Built-in support for scanning repositories with Gitleaks and TruffleHog
- **Comprehensive Reporting**: Generate detailed reports of discovered secrets


## License

This project is licensed under the MIT License - see the LICENSE file for details.
```
