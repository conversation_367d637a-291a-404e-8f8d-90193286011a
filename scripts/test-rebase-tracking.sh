#!/bin/bash
# Test script to demonstrate how rebase changes commit hashes
# This shows why the original commit hash becomes invalid after rebase

set -e

# Source the tracking functions
# shellcheck disable=SC1091
source "$(dirname "$0")/track-commits.sh"

echo "🧪 TESTING REBASE COMMIT HASH TRACKING"
echo "======================================"

# Create a temporary test repository
TEST_DIR="/tmp/rebase-test-$(date +%s)"
mkdir -p "$TEST_DIR"
cd "$TEST_DIR"

echo "📁 Created test directory: $TEST_DIR"

# Initialize git repo
git init
git config user.name "Test User"
git config user.email "<EMAIL>"

# Create initial commit
echo "# Initial file" > README.md
git add README.md
git commit -m "Initial commit"
echo "✅ Created initial commit: $(git rev-parse HEAD)"

# Create a feature branch
git checkout -b feature/test-secret
echo "SECRET_KEY=abc123" > secret.txt
git add secret.txt
git commit -m "Add secret"

# Record the original commit
original_hash=$(git rev-parse HEAD)
echo "📝 Original commit hash: $original_hash"

# Make a change to main
git checkout main
echo "# Updated README" >> README.md
git add README.md
git commit -m "Update README on main"
echo "✅ Updated main branch: $(git rev-parse HEAD)"

# Now rebase the feature branch
git checkout feature/test-secret
echo "🔄 Before rebase - commit hash: $(git rev-parse HEAD)"

git rebase main

# Check the new hash after rebase
new_hash=$(git rev-parse HEAD)
echo "🔄 After rebase - commit hash: $new_hash"

echo ""
echo "🔍 ANALYSIS:"
echo "============"
echo "Original hash: $original_hash"
echo "New hash:      $new_hash"

if [[ "$original_hash" == "$new_hash" ]]; then
    echo "❌ ERROR: Hashes are the same (this shouldn't happen after rebase)"
else
    echo "✅ SUCCESS: Rebase changed the commit hash"
    echo "   The original hash ($original_hash) no longer exists"
    echo "   The new hash ($new_hash) is what should be tracked for dangling detection"
fi

echo ""
echo "🧪 TESTING COMMIT EXISTENCE:"
echo "============================"

# Test if original commit still exists
if git cat-file -e "$original_hash" 2>/dev/null; then
    echo "⚠️  Original commit still exists (might be in reflog)"
else
    echo "❌ Original commit does not exist"
fi

# Test if new commit exists
if git cat-file -e "$new_hash" 2>/dev/null; then
    echo "✅ New commit exists"
else
    echo "❌ New commit does not exist (this would be an error)"
fi

echo ""
echo "🔍 SIMULATING GITHUB GRAPHQL TEST:"
echo "=================================="
echo "If we test the original hash '$original_hash' against GitHub's GraphQL API,"
echo "it would return 'invalid' because that specific hash no longer exists"
echo "after the rebase operation."

echo ""
echo "💡 SOLUTION:"
echo "============"
echo "The tracking system should:"
echo "1. Record the original hash before rebase"
echo "2. Mark it as 'replaced' after rebase"
echo "3. Record the new hash after rebase"
echo "4. Use the NEW hash for dangling commit detection"

# Cleanup
cd /
rm -rf "$TEST_DIR"
echo ""
echo "🧹 Cleaned up test directory"
echo "✅ Test completed successfully"
