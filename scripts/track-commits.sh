#!/bin/bash
# Commit tracking system for Dangler test repository creation
# This script records all commits created during test scenarios

set -e

CWD="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Initialize tracking
COMMIT_LOG="${CWD}/commit-tracking.json"

# Function to record a commit
record_commit() {
    local scenario="$1"
    local description="$2"
    local branch="$3"
    local commit_hash="$4"
    local commit_message="$5"
    local status="$6"  # created, force-pushed, deleted, orphaned, etc.
    local timestamp entry
    timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")

    # Create JSON entry
    entry=$(cat <<EOF
{
    "scenario": "$scenario",
    "description": "$description",
    "branch": "$branch",
    "commit_hash": "$commit_hash",
    "commit_message": "$commit_message",
    "status": "$status",
    "timestamp": "$timestamp"
}
EOF
)

    # Append to tracking file
    if [[ ! -f "$COMMIT_LOG" ]]; then
        echo "[]" > "$COMMIT_LOG"
    fi

    # Add entry to JSON array
    jq ". += [$entry]" "$COMMIT_LOG" > "${COMMIT_LOG}.tmp" && mv "${COMMIT_LOG}.tmp" "$COMMIT_LOG"

    echo "📝 Tracked: $scenario - $description ($commit_hash)"
}

# Function to get current commit hash
get_commit_hash() {
    git rev-parse HEAD
}

# Function to get last commit message
get_commit_message() {
    git log -1 --pretty=format:"%s"
}

# Function to record current commit
record_current_commit() {
    local scenario="$1"
    local description="$2"
    local branch="$3"
    local status="$4"
    local hash message

    hash=$(get_commit_hash)
    message=$(get_commit_message)

    record_commit "$scenario" "$description" "$branch" "$hash" "$message" "$status"
}

# Function to update commit status (cleaner than jq manipulation)
update_commit_status() {
    local scenario="$1"
    local description_pattern="$2"
    local new_status="$3"

    if [[ ! -f "$COMMIT_LOG" ]]; then
        echo "❌ Error: Commit log $COMMIT_LOG not found"
        return 1
    fi

    # Find the commit hash
    local hash
    hash=$(jq -r ".[] | select(.scenario == \"$scenario\" and (.description | contains(\"$description_pattern\"))) | .commit_hash" "$COMMIT_LOG")

    if [[ -z "$hash" || "$hash" == "null" ]]; then
        echo "❌ Error: No commit found for scenario '$scenario' with description containing '$description_pattern'"
        return 1
    fi

    # Update the status
    jq "(.[] | select(.commit_hash == \"$hash\")).status = \"$new_status\"" "$COMMIT_LOG" > "${COMMIT_LOG}.tmp" && mv "${COMMIT_LOG}.tmp" "$COMMIT_LOG"

    echo "✅ Updated commit ${hash:0:8} status to '$new_status'"
}

# Function to mark original commit as replaced and record the new one
record_commit_replacement() {
    local scenario="$1"
    local original_description_pattern="$2"
    local new_hash="$3"
    local new_description="$4"
    local new_status="$5"
    local branch="$6"
    local message="$7"

    # Mark the original commit as replaced
    update_commit_status "$scenario" "$original_description_pattern" "replaced"

    # Record the new commit
    record_commit "$scenario" "$new_description" "$branch" "$new_hash" "$message" "$new_status"

    echo "🔄 Recorded commit replacement: $original_description_pattern -> $new_description"
}

# Function to analyze tracking results
analyze_tracking() {
    echo ""
    echo "🔍 COMMIT TRACKING ANALYSIS"
    echo "=========================="

    local total_commits

    total_commits=$(jq 'length' "$COMMIT_LOG")
    echo "Total commits created: $total_commits"

    echo ""
    echo "📊 Commits by status:"
    jq -r 'group_by(.status) | .[] | "\(length) \(.[0].status) commits"' "$COMMIT_LOG"

    echo ""
    echo "📋 Commits by scenario:"
    jq -r 'group_by(.scenario) | .[] | "\(length) commits in \(.[0].scenario)"' "$COMMIT_LOG"

    echo ""
    echo "🎯 Expected dangling commits (should be detected by Dangler):"
    jq -r '.[] | select(.status == "dangling" or .status == "force-pushed" or .status == "orphaned") | "  \(.commit_hash[0:8]) - \(.scenario): \(.description)"' "$COMMIT_LOG"

    echo ""
    echo "✅ Regular commits (should NOT be detected by Dangler):"
    jq -r '.[] | select(.status == "regular" or .status == "merged") | "  \(.commit_hash[0:8]) - \(.scenario): \(.description)"' "$COMMIT_LOG"

    echo ""
    echo "📄 Full tracking log saved to: $COMMIT_LOG"
}

# Function to compare with Dangler results
compare_with_dangler() {
    local info
    local dangler_output="$1"

    echo ""
    echo "🔍 COMPARING WITH DANGLER RESULTS"
    echo "================================="

    # Extract commit hashes from Dangler output
    local dangler_commits
    dangler_commits=$(echo "$dangler_output" | grep -oE '[a-f0-9]{40}' | sort | uniq)

    echo "Commits found by Dangler:"
    echo "$dangler_commits" | while read -r commit; do
        if [[ -n "$commit" ]]; then
            info=$(jq -r ".[] | select(.commit_hash | startswith(\"$commit\")) | \"\(.scenario): \(.description)\"" "$COMMIT_LOG")
            if [[ -n "$info" ]]; then
                echo "  ✅ $commit - $info"
            else
                echo "  ❓ $commit - UNKNOWN (not in our tracking)"
            fi
        fi
    done

    echo ""
    echo "Expected dangling commits NOT found by Dangler:"
    jq -r '.[] | select(.status == "dangling" or .status == "force-pushed" or .status == "orphaned") | .commit_hash' "$COMMIT_LOG" | while read -r expected; do
        if ! echo "$dangler_commits" | grep -q "$expected"; then
            info=$(jq -r ".[] | select(.commit_hash == \"$expected\") | \"\(.scenario): \(.description)\"" "$COMMIT_LOG")
            echo "  ❌ $expected - $info"
        fi
    done
}

# Export functions for use in other scripts
export -f record_commit
export -f record_current_commit
export -f update_commit_status
export -f record_commit_replacement
export -f get_commit_hash
export -f get_commit_message
export -f analyze_tracking
export -f compare_with_dangler

echo "🎯 Commit tracking system initialized"
echo "📄 Tracking file: $COMMIT_LOG"
