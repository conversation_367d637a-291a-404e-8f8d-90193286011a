package main

import (
	"fmt"
	"log"
	"os"
	"os/exec"
	"strings"
	"time"
)

// runGitCommand executes a git command and returns the output
func runGitCommand(args []string) (string, error) {
	cmd := exec.Command("git", args...)
	output, err := cmd.CombinedOutput()
	return strings.TrimSpace(string(output)), err
}

// FetchDanglingCommit fetches a dangling commit from a remote repository and checks it out into a new branch
func FetchDanglingCommit(repoPath string, commit string) error {
	fmt.Printf("🔍 Testing commit %s...\n", commit)

	// Fetch commit from remote
	gitArgs := []string{
		"-C", repoPath,
		"--work-tree", repoPath,
		"fetch",
		"--quiet",
		"--no-auto-gc",
		"origin", commit,
	}
	_, err := runGitCommand(gitArgs)
	if err != nil {
		return fmt.Errorf("Failed to fetch commit %s: %w", commit, err)
	}

	// Checkout the commit into a new branch
	branchName := fmt.Sprintf("dangling_%s", commit[:8])
	gitArgs = []string{
		"-C", repoPath,
		"--work-tree", repoPath,
		"checkout",
		"--quiet",
		"-b", branchName,
		commit,
	}
	_, err = runGitCommand(gitArgs)
	if err != nil {
		return fmt.Errorf("❌ Failed to checkout commit %s to branch %s: %w",
			commit, branchName, err)
	}

	fmt.Printf("✅ Successfully fetched and checked out commit %s to branch %s\n", commit[:8], branchName)
	return nil
}

// checkCommitReachability checks if a commit is reachable from any ref
func checkCommitReachability(repoPath string, commit string) (bool, error) {
	// Check if commit is reachable from any branch
	gitArgs := []string{
		"-C", repoPath,
		"branch",
		"--contains", commit,
	}
	output, err := runGitCommand(gitArgs)
	if err == nil && strings.TrimSpace(output) != "" {
		fmt.Printf("🔗 Commit %s is reachable from branches: %s\n", commit[:8], strings.ReplaceAll(output, "\n", ", "))
		return true, nil
	}

	// Check if commit is reachable from any tag
	gitArgs = []string{
		"-C", repoPath,
		"tag",
		"--contains", commit,
	}
	output, err = runGitCommand(gitArgs)
	if err == nil && strings.TrimSpace(output) != "" {
		fmt.Printf("🏷️  Commit %s is reachable from tags: %s\n", commit[:8], strings.ReplaceAll(output, "\n", ", "))
		return true, nil
	}

	// Check if commit exists at all
	gitArgs = []string{
		"-C", repoPath,
		"cat-file",
		"-e", commit,
	}
	_, err = runGitCommand(gitArgs)
	if err != nil {
		fmt.Printf("💀 Commit %s does not exist in repository\n", commit[:8])
		return false, fmt.Errorf("commit does not exist")
	}

	fmt.Printf("🚨 Commit %s exists but is NOT reachable from any branch or tag (TRULY DANGLING)\n", commit[:8])
	return false, nil
}

// analyzeCommit shows commit details
func analyzeCommit(repoPath string, commit string) error {
	// Get commit message
	gitArgs := []string{
		"-C", repoPath,
		"log",
		"--format=%s",
		"-n", "1",
		commit,
	}
	message, err := runGitCommand(gitArgs)
	if err != nil {
		return fmt.Errorf("failed to get commit message: %w", err)
	}

	// Get commit author and date
	gitArgs = []string{
		"-C", repoPath,
		"log",
		"--format=%an <%ae> - %ad",
		"--date=short",
		"-n", "1",
		commit,
	}
	author, err := runGitCommand(gitArgs)
	if err != nil {
		return fmt.Errorf("failed to get commit author: %w", err)
	}

	// Get files changed
	gitArgs = []string{
		"-C", repoPath,
		"show",
		"--name-only",
		"--format=",
		commit,
	}
	files, err := runGitCommand(gitArgs)
	if err != nil {
		return fmt.Errorf("failed to get changed files: %w", err)
	}

	fmt.Printf("📝 Message: %s\n", message)
	fmt.Printf("👤 Author: %s\n", author)
	fmt.Printf("📁 Files: %s\n", strings.ReplaceAll(files, "\n", ", "))

	return nil
}

func main() {
	if len(os.Args) < 3 {
		fmt.Println("Usage: go run validate-dangling.go <repo-url> <commit1> [commit2] [commit3] ...")
		fmt.Println("Example: go run validate-dangling.go https://github.com/user/repo.git abc123 def456")
		os.Exit(1)
	}

	repoURL := os.Args[1]
	commits := os.Args[2:]

	// Create temporary directory
	tempDir := fmt.Sprintf("/tmp/dangler-test-%d", time.Now().Unix())
	defer os.RemoveAll(tempDir)

	fmt.Printf("🚀 Cloning repository %s to %s\n", repoURL, tempDir)

	// Clone repository with all refs
	cloneArgs := []string{
		"clone",
		"--quiet",
		"-c", "remote.origin.fetch=+refs/*:refs/remotes/origin/*",
		repoURL,
		tempDir,
	}
	_, err := runGitCommand(cloneArgs)
	if err != nil {
		log.Fatalf("Failed to clone repository: %v", err)
	}

	fmt.Printf("✅ Repository cloned successfully\n\n")

	// Test each commit
	for i, commit := range commits {
		fmt.Printf("=== Testing Commit %d/%d: %s ===\n", i+1, len(commits), commit)

		// First check if it's reachable without fetching
		reachable, err := checkCommitReachability(tempDir, commit)
		if err != nil {
			fmt.Printf("⚠️  Commit %s: %v\n", commit[:8], err)
		}

		if !reachable {
			// Try to fetch and checkout the dangling commit
			err = FetchDanglingCommit(tempDir, commit)
			if err != nil {
				fmt.Printf("❌ %v\n", err)
			} else {
				// Analyze the commit
				err = analyzeCommit(tempDir, commit)
				if err != nil {
					fmt.Printf("⚠️  Failed to analyze commit: %v\n", err)
				}
			}
		} else {
			// Still analyze reachable commits to see what they contain
			err = analyzeCommit(tempDir, commit)
			if err != nil {
				fmt.Printf("⚠️  Failed to analyze commit: %v\n", err)
			}
		}

		fmt.Println()
	}

	fmt.Printf("🎯 Validation complete! Check the results above to see which commits are truly dangling.\n")
}
