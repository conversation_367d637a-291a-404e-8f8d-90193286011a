#!/bin/bash
# Debug tools to identify missing detections and reachability issues

set -e

CWD="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Source the tracking system if it exists
if [[ -f "${CWD}/track-commits.sh" ]]; then
    # shellcheck disable=SC1091
    source "${CWD}/track-commits.sh"
else
    echo "⚠️  Warning: track-commits.sh not found. Some functions may not work."
    COMMIT_LOG="commit-tracking-github.json"
fi

# 1. Git Repository State Snapshots
capture_repo_state() {
    local suffix="$1"
    local output_dir="debug-snapshots"
    mkdir -p "$output_dir"

    echo "📸 Capturing repository state: $suffix"

    # All commits with graph
    git log --all --oneline --graph > "$output_dir/commits-$suffix.txt"

    # All refs and their commit hashes
    git for-each-ref --format="%(refname) %(objectname)" > "$output_dir/refs-$suffix.txt"

    # Branch information
    git branch -a > "$output_dir/branches-$suffix.txt"

    # Remote information
    git remote -v > "$output_dir/remotes-$suffix.txt" 2>/dev/null || echo "No remotes" > "$output_dir/remotes-$suffix.txt"

    echo "📄 State saved to $output_dir/*-$suffix.txt"
}

# 2. Commit Reachability Testing
test_reachability() {
    local commit="$1"
    local results=""

    # Test against HEAD
    if git merge-base --is-ancestor "$commit" HEAD 2>/dev/null; then
        results="$results REACHABLE_FROM_HEAD"
    else
        results="$results NOT_REACHABLE_FROM_HEAD"
    fi

    # Test against all branches
    git branch -a | sed 's/^[* ] //' | while read -r branch; do
        if [[ -n "$branch" && "$branch" != "HEAD" ]]; then
            if git merge-base --is-ancestor "$commit" "$branch" 2>/dev/null; then
                echo "$commit: REACHABLE from $branch"
            fi
        fi
    done

    # Test if commit exists at all
    if git cat-file -e "$commit" 2>/dev/null; then
        results="$results EXISTS"
    else
        results="$results MISSING"
    fi

    echo "$commit:$results"
}

# Test all tracked commits
test_all_reachability() {
    echo "🔍 Testing reachability of all tracked commits..."

    if [[ ! -f "$COMMIT_LOG" ]]; then
        echo "❌ No commit tracking log found. Run a test script first."
        return 1
    fi

    echo "Commit | Exists | Reachability | Expected Status"
    echo "-------|--------|-------------|----------------"

    jq -r '.[] | "\(.commit_hash) \(.status) \(.scenario) \(.description)"' "$COMMIT_LOG" | while read -r hash status scenario desc; do
        if [[ -n "$hash" ]]; then
            reachability=$(test_reachability "$hash")
            echo "$hash | $reachability | $status | $scenario: $desc"
        fi
    done
}

# 3. Dangler vs Git Comparison
compare_commit_sources() {
    local dangler_commits
    local dangler_output="$1"

    echo "🔍 Comparing Dangler results with Git state..."

    # Extract commits from Dangler output
    dangler_commits=$(echo "$dangler_output" | grep -oE '[a-f0-9]{40}' | sort | uniq)

    echo ""
    echo "=== Git's reachable commits ==="
    git rev-list --all | sort > /tmp/git-reachable.txt
    echo "Count: $(wc -l < /tmp/git-reachable.txt)"

    echo ""
    echo "=== Git's all commits (including unreachable) ==="
    git fsck --unreachable 2>/dev/null | grep "commit" | awk '{print $3}' | sort > /tmp/git-unreachable.txt
    cat /tmp/git-reachable.txt /tmp/git-unreachable.txt | sort | uniq > /tmp/git-all.txt
    echo "Count: $(wc -l < /tmp/git-all.txt)"

    echo ""
    echo "=== Dangler's detected commits ==="
    echo "$dangler_commits" > /tmp/dangler-commits.txt
    echo "Count: $(echo "$dangler_commits" | wc -l)"

    echo ""
    echo "=== Commits in Git but NOT detected by Dangler ==="
    comm -23 /tmp/git-unreachable.txt /tmp/dangler-commits.txt | head -10

    echo ""
    echo "=== Commits detected by Dangler but NOT in Git ==="
    comm -13 /tmp/git-all.txt /tmp/dangler-commits.txt | head -10

    # Cleanup
    rm -f /tmp/git-*.txt /tmp/dangler-commits.txt
}

# 5. Force Push Detection Validation (CORRECTED)
validate_force_pushes() {
    echo "🔍 Validating force push detection..."

    echo ""
    echo "=== Actual resets from reflog ==="
    git reflog --all | grep "reset: moving to" | head -10

    echo ""
    echo "=== Branch pointer movements ==="
    git reflog --all | grep -E "(reset|checkout)" | head -10

    echo ""
    echo "=== Commits that were reset away ==="
    # Look for commits that were reset away
    git reflog --all --format="%H %gs" | grep "reset: moving to" | while read -r hash message; do
        echo "Reset detected: $hash - $message"
    done
}

# Main analysis function
run_detection_analysis() {
    local dangler_output
    local dangler_output_file="$1"

    if [[ -z "$dangler_output_file" || ! -f "$dangler_output_file" ]]; then
        echo "Usage: run_detection_analysis <dangler-output-file>"
        echo "Example: run_detection_analysis dangler-output.txt"
        return 1
    fi

    dangler_output=$(cat "$dangler_output_file")

    echo "🔍 DETECTION GAP ANALYSIS"
    echo "========================"

    capture_repo_state "current"
    echo ""

    test_all_reachability
    echo ""

    compare_commit_sources "$dangler_output"
    echo ""

    validate_force_pushes
    echo ""

    echo "✅ Analysis complete! Check debug-snapshots/ for detailed state files."
}

# Export functions
export -f capture_repo_state
export -f test_reachability
export -f test_all_reachability
export -f compare_commit_sources
export -f validate_force_pushes
export -f run_detection_analysis

echo "🔧 Detection debugging tools loaded!"
echo "📋 Available functions:"
echo "  - capture_repo_state <suffix>"
echo "  - test_all_reachability"
echo "  - compare_commit_sources <dangler_output>"
echo "  - validate_force_pushes"
echo "  - run_detection_analysis <dangler-output-file>"
