#!/bin/bash
# This script automates the creation of a test repository for <PERSON><PERSON> on GitHub
# It creates all the scenarios described in the TESTING.md file
# Now includes commit tracking for analysis

set -e  # Exit on error

CWD="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Source the commit tracking system
# shellcheck disable=SC1091
source "${CWD}/track-commits.sh"

# Check if GitHub CLI is installed
if ! command -v gh &> /dev/null; then
    echo "GitHub CLI (gh) is not installed. Please install it first."
    exit 1
fi

# Check if user is authenticated with GitHub
if ! gh auth status &> /dev/null; then
    echo "You are not authenticated with GitHub. Please run 'gh auth login' first."
    exit 1
fi

# Check if Jq is installed
if ! command -v jq &> /dev/null; then
    echo "Jq is not installed. Please install it first."
    exit 1
fi

# Create a new repository
echo "Creating a new repository on GitHub..."
REPO_NAME="hidden-secrets"

rm -rf "${CWD:?}/${REPO_NAME}"

gh repo create "${REPO_NAME}" \
    --public \
    --description "${REPO_NAME} Test Repository"

git -C "${CWD}" clone "$(gh api user | jq -r '.html_url')/${REPO_NAME}.git"

cd "${CWD}/${REPO_NAME}"

OWNER=$(gh api user | jq -r '.login')



# Initialize with a README
echo "# ${REPO_NAME} Test Repository" > README.md
git add README.md
git commit -m "Initial commit"
git push origin main

# Disable secret scanning and push protection
echo "Disabling secret scanning and push protection for repository..."
gh api --silent --method PATCH "repos/${OWNER}/${REPO_NAME}" \
    -f "security_and_analysis[secret_scanning][status]=disabled" \
    -f "security_and_analysis[secret_scanning_push_protection][status]=disabled"

# Track the initial commit
record_current_commit "Initial" "Repository initialization" "main" "regular"

echo "Repository created: ${REPO_NAME}"
echo "Starting to create test scenarios..."

# Scenario 1: Dangling Commit (Deleted Branch)
echo "Creating Scenario 1: Dangling Commit (Deleted Branch)..."
git checkout -b feature/secret-1
echo "AWS_ACCESS_KEY_ID=AKIALALEMEL33243OLIB" > credentials1.txt
git add credentials1.txt
git commit -m "Add AWS credentials"
record_current_commit "Scenario-1" "AWS credentials commit (will be dangling)" "feature/secret-1" "created"
git push origin feature/secret-1

# Delete the branch
git checkout main
git push origin --delete feature/secret-1
# Update status to dangling
update_commit_status "Scenario-1" "AWS credentials commit" "dangling"

# Scenario 2: Force-Pushed Commit
echo "Creating Scenario 2: Force-Pushed Commit..."
git checkout main
git checkout -b feature/secret-2
echo "GITHUB_TOKEN=****************************************" > credentials2.txt
git add credentials2.txt
git commit -m "Add GitHub token"
record_current_commit "Scenario-2" "GitHub token commit (will be force-pushed)" "feature/secret-2" "force-pushed"
git push origin feature/secret-2

# Now remove the secret and force push
git reset --hard HEAD~1
echo "# This file is now empty" > credentials2.txt
git add credentials2.txt
git commit -m "Update credentials file (removed token)"
record_current_commit "Scenario-2" "Clean credentials file (replacement)" "feature/secret-2" "regular"
git push origin feature/secret-2 --force

# Scenario 3: Squashed Commits
echo "Creating Scenario 3: Squashed Commits..."
git checkout main
git checkout -b feature/secret-3

# Add multiple commits
echo "# Initial file" > config3.txt
git add config3.txt
git commit -m "Add config file"
record_current_commit "Scenario-3" "Initial config file" "feature/secret-3" "regular"

# Add the secret in a commit
echo "STRIPE_SECRET_KEY=rk_prod_51OuEMLAlTWGaDypquDn9aZigaJOsa9NR1w1BxZXs9JlYsVVkv5XDu6aLmAxwt5Tgun5WcSwQMKzQyqV16c9iD4sx00BRijuoon" >> config3.txt
git add config3.txt
git commit -m "Add Stripe key"
record_current_commit "Scenario-3" "Stripe key commit (will be squashed)" "feature/secret-3" "squashed"

# Remove the secret in a subsequent commit
# For macOS compatibility, we need to handle sed differently
if [[ "${OSTYPE}" == "darwin"* ]]; then
    # macOS (BSD) sed
    sed -i '' '/STRIPE_SECRET_KEY/d' config3.txt
else
    # GNU sed (Linux)
    sed -i '/STRIPE_SECRET_KEY/d' config3.txt
fi
git add config3.txt
git commit -m "Remove sensitive data"
record_current_commit "Scenario-3" "Remove sensitive data" "feature/secret-3" "squashed"

# Add some other changes
echo "# Some other changes" >> config3.txt
git add config3.txt
git commit -m "Update config file"
record_current_commit "Scenario-3" "Update config file" "feature/secret-3" "squashed"

git push origin feature/secret-3

# Create PR and merge with squash
gh pr create --title "Feature with secret to be squashed" --body "This PR contains a secret that will be hidden in squashed commits"
gh pr merge feature/secret-3 --squash --delete-branch
record_current_commit "Scenario-3" "Squashed merge commit" "main" "regular"

# Scenario 4: Rebased Commits
echo "Creating Scenario 4: Rebased Commits..."
git checkout main
git pull origin main  # Pull latest changes from previous scenarios
git checkout -b feature/secret-4

# Add a secret
echo "GOOGLE_API_KEY=AIzaSyDUm6NHrS54isThKUDlyPdRxDsXm7ct_wY" > key4.txt
git add key4.txt
git commit -m "Add Google API key"
# Store the original commit hash before rebase
original_commit=$(get_commit_hash)
record_current_commit "Scenario-4" "Google API key commit (original, before rebase)" "feature/secret-4" "created"

# Make another change to main
git checkout main
echo "# Important update" >> README.md
git add README.md
git commit -m "Update README"
record_current_commit "Scenario-4" "README update on main" "main" "regular"
git push origin main

# Now rebase the feature branch
git checkout feature/secret-4
git rebase main

# Record the NEW commit hash after rebase (this is the one that will become dangling)
rebased_commit=$(get_commit_hash)
echo "🔄 Rebase changed commit hash: $original_commit -> $rebased_commit"
record_commit_replacement "Scenario-4" "Google API key commit (original" "$rebased_commit" "Google API key commit (after rebase, will be dangling)" "created" "feature/secret-4" "Add Google API key"

# Remove the secret and continue
git reset --soft HEAD~1
echo "# Key removed for security" > key4.txt
git add key4.txt
git commit -m "Remove Google API key"
record_current_commit "Scenario-4" "Clean key file (after rebase)" "feature/secret-4" "regular"
git push origin feature/secret-4 --force

# Create PR but don't merge - leave the rebased commits dangling
gh pr create --title "Rebased feature branch" --body "This PR demonstrates rebased commits"
# Don't merge - the rebased commits should remain dangling
# Delete the branch to make commits truly dangling
git checkout main
git push origin --delete feature/secret-4
# Update status to dangling for the rebased commit (the one that will actually be dangling)
update_commit_status "Scenario-4" "Google API key commit (after rebase)" "dangling"

# Scenario 5: PR Force-Push
echo "Creating Scenario 5: PR Force-Push..."
git checkout main
git checkout -b feature/secret-5

# Add a secret
echo "OPENAI_API_KEY=***************************************************" > api5.txt
git add api5.txt
git commit -m "Add OpenAI API key"
record_current_commit "Scenario-5" "OpenAI API key commit (will be force-pushed)" "feature/secret-5" "created"
git push origin feature/secret-5

# Create a PR
gh pr create --title "Feature with secret to be force-pushed" --body "This PR initially contains a secret"

# Now force-push to remove the secret
git reset --hard HEAD~1
echo "# API key stored in environment variables" > api5.txt
git add api5.txt
git commit -m "Use environment variables instead of hardcoded API key"
record_current_commit "Scenario-5" "Clean API file (replacement)" "feature/secret-5" "regular"
git push origin feature/secret-5 --force

# Don't merge the PR - leave the force-pushed commits dangling
# gh pr merge feature/secret-5 --merge
# Delete the branch to make commits truly dangling
git checkout main
git push origin --delete feature/secret-5
# Update status to dangling
update_commit_status "Scenario-5" "OpenAI API key commit" "dangling"

# Scenario 6: Orphaned Commits
echo "Creating Scenario 6: Orphaned Commits..."
git checkout main
git checkout -b feature/secret-6

# Add a secret
echo "MAILCHIMP_API_KEY=a1b2c3d4e5f6g7h8i9j0-us1" > api6.txt
git add api6.txt
git commit -m "Add Mailchimp API key"
record_current_commit "Scenario-6" "Mailchimp API key commit (will be orphaned)" "feature/secret-6" "created"
git push origin feature/secret-6

# Create another commit on top
echo "# Additional content" >> api6.txt
git add api6.txt
git commit -m "Update API file"
record_current_commit "Scenario-6" "Update API file (will be orphaned)" "feature/secret-6" "created"
git push origin feature/secret-6

# Now reset to main and force push, orphaning the commits
git reset --hard main
echo "# Clean file" > api6.txt
git add api6.txt
git commit -m "Clean API file"
record_current_commit "Scenario-6" "Clean API file (replacement)" "feature/secret-6" "regular"
git push origin feature/secret-6 --force

# Create PR but don't merge - leave the orphaned commits dangling
gh pr create --title "Feature with orphaned commits" --body "This PR has orphaned commits"
# Don't merge - the orphaned commits should remain dangling
# Delete the branch to make commits truly dangling
git checkout main
git push origin --delete feature/secret-6
# Update status to orphaned for the two commits
update_commit_status "Scenario-6" "Mailchimp API key commit" "orphaned"
update_commit_status "Scenario-6" "Update API file" "orphaned"

# Scenario 7: Temporary Commits
echo "Creating Scenario 7: Temporary Commits..."
git checkout main
git pull origin main

# Create a new branch
git checkout -b feature/secret-7

# Make some legitimate changes
echo "# Configuration file" > config7.txt
git add config7.txt
git commit -m "Add configuration file"
record_current_commit "Scenario-7" "Configuration file" "feature/secret-7" "regular"

# Add a temporary secret for testing
echo "TWILIO_AUTH_TOKEN=0a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p" >> config7.txt
git add config7.txt
git commit -m "Add temporary Twilio token for testing"
record_current_commit "Scenario-7" "Temporary Twilio token (will be dangling)" "feature/secret-7" "created"
git push origin feature/secret-7

# Remove the secret before merging
git reset --soft HEAD~1
echo "# Configuration file" > config7.txt
echo "# Token removed" >> config7.txt
git add config7.txt
git commit -m "Remove temporary token"
record_current_commit "Scenario-7" "Clean config file (replacement)" "feature/secret-7" "regular"
git push origin feature/secret-7 --force

# Create PR but don't merge - leave the temporary commits dangling
gh pr create --title "Feature with temporary commits" --body "This PR had temporary testing commits"
# Don't merge - the temporary commits should remain dangling
# Delete the branch to make commits truly dangling
git checkout main
git push origin --delete feature/secret-7
# Update status to dangling
update_commit_status "Scenario-7" "Temporary Twilio token" "dangling"

# Scenario 8: Regular Commit (Control)
echo "Creating Scenario 8: Regular Commit (Control)..."
git checkout main
git pull origin main

# Create a new branch
git checkout -b feature/regular-secret

# Add a secret in a regular commit
echo "SLACK_TOKEN=xapp-1-IEMF8IMY1OQ-4037076220459-85c370b433e366de369c4ef5abdf41253519266982439a75af74a3d68d543fb6" > slack.txt
git add slack.txt
git commit -m "Add Slack Access Token"
record_current_commit "Scenario-8" "Slack secret commit (will be merged)" "feature/regular-secret" "regular"
git push origin feature/regular-secret

# Create PR and merge normally
gh pr create --title "Feature with regular secret" --body "This PR contains a secret in a regular commit"
gh pr merge feature/regular-secret --merge
# Note: --merge creates a merge commit, but we already tracked the original commit

echo "All scenarios created successfully!"
echo "Repository URL: https://github.com/${OWNER}/${REPO_NAME}"

# Analyze the tracking results
analyze_tracking

echo ""
echo "🚀 NEXT STEPS:"
echo "=============="
echo "1. Run Dangler against this repository:"
echo "   dangler github -d -r ${OWNER}/${REPO_NAME} -t \$(gh auth token) > dangler-output.txt"
echo ""
echo "2. Compare results with tracking:"
echo "   source scripts/track-commits.sh"
echo "   compare_with_dangler \"\$(cat dangler-output.txt)\""
echo ""
echo "3. Check for missing detections:"
echo "   cat $COMMIT_LOG | jq '.[] | select(.status == \"dangling\" or .status == \"force-pushed\")'"
