### Project Description
You are building an Alpine.js web application that allows users to upload one or multiple Octopi JSON files. The application will then parse the JSON files and display the data in a table. The user should be able to filter the data in the table by various criteria. The user should also be able to download the data in the table as a CSV file. The user should be able to sort the data in the table by various criteria. The user should be able to search the data in the table by various criteria. The user should be able to paginate the data in the table

### Tech Stack:
HTML/CSS/JavaScript, Tailwind CSS, and Alpine.js
