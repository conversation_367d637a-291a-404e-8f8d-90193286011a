{
    "repo_url": "https://github.com/hologram/support2",
    "detector_name": "A<PERSON>",
    "decoder_name": "PLAIN",
    "verified": true,
    "raw": "AKIAZFYRNP734DFKC4",
    "commit": "a6dcb574080c218be1f0d89bcb4a373cbf185c7a",
    "file": "config/filesystems.php",
    "email": "dispatch <<EMAIL>>",
    "line": 73,
    "link": "https://github.com/hologram/support2/blob/a6dcb574080c218be1f0d89bcb4a373cbf185c7a/config/filesystems.php#L73",
    "timestamp": "2024-07-11 08:19:41 +0000",
    "ExtraData": {
     "account": "*************",
     "arn": "arn:aws:iam::************:user/yoti-rw",
     "resource_type": "Access key",
     "rotation_guide": "https://howtorotate.com/docs/tutorials/aws/",
     "user_id": "AIDAZFYRNP7VGKLG64TJ4"
    },
    "dangler_commit": false
}
{
    "repo_url": "https://github.com/hologram/reporter",
    "detector_name": "AWS",
    "decoder_name": "PLAIN",
    "verified": true,
    "raw": "AKIA46UKC7E83DFJOWJSSMI",
    "commit": "c426ee6f5030bb9b2760dbf7b0c2cba17bfe2138",
    "file": "app/tasks/dispatch_to_channels.py",
    "email": "ant6581 <<EMAIL>>",
    "line": 32,
    "link": "https://github.com/hologram/reporter/blob/c426ee6f5030bb9b2760dbf7b0c2cba17bfe2138/app/tasks/dispatch_to_channels.py#L32",
    "timestamp": "2024-03-29 15:16:31 +0000",
    "ExtraData": {
     "account": "***********",
     "arn": "arn:aws:iam::************:user/ras",
     "resource_type": "Access key",
     "rotation_guide": "https://howtorotate.com/docs/tutorials/aws/",
     "user_id": "AIDA46UKCN5BX3OJVXDDN",
     "secret": "bM4W63K/5H8UaUa/+X//+HDJDWSSJssgugsue73",
    },
    "dangler_commit": true
}
{
    "repo_url": "https://github.com/hologram/rd/svc/etl/c4s-events",
    "detector_name": "Gitlab",
    "decoder_name": "PLAIN",
    "verified": true,
    "raw": "glpat-F87Vsdjje84h8fkfffj",
    "commit": "94dc69394291d0d5fa91f6d78e82d99bdc30cbac",
    "file": "requirements.txt",
    "email": "Darc Samodid <<EMAIL>>",
    "line": 1,
    "link": "https://github.com/hologram/c4s-events/blob/94dc69394291d0d5fa91f6d78e82d99bdc30cbac/requirements.txt#L1",
    "timestamp": "2024-12-01 09:14:01 +0000",
    "ExtraData": {
     "rotation_guide": "https://howtorotate.com/docs/tutorials/gitlab/",
     "version": "2"
    },
    "dangler_commit": false
}
