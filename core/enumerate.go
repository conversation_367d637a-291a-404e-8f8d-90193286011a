package core

import (
	"context"
	"fmt"
	"sort"
	"strings"

	"github.com/google/go-github/v72/github"
	"github.com/rs/zerolog/log"
)

// ContributorsByContributions implements sort.Interface for []ReconReport based on Contributions field
type ContributorsByContributions []ReconReport

// Len returns the length of the ContributorsByContributions slice
func (c ContributorsByContributions) Len() int {
	return len(c)
}

// Swap exchanges elements at indices i and j
func (c ContributorsByContributions) Swap(i, j int) {
	c[i], c[j] = c[j], c[i]
}

// Less reports whether element i should sort before element j
// Sorts in descending order (higher contributions first)
func (c ContributorsByContributions) Less(i, j int) bool {
	return c[i].Contributions > c[j].Contributions
}

// EnumerateOrganization enumerates GitHub organization members, repositories and contributors
// Returns a slice of ReconReport containing users and emails associated with the organization
func (g *Github) EnumerateOrganization(ctx context.Context, orgName string) ([]ReconReport, error) {
	var reconReport []ReconReport
	uniqueUsers := make(map[string]bool)
	uniqueEmails := make(map[string]bool)
	query := fmt.Sprintf("org:%s", orgName)

	// Get organization repositories
	orgRepos, err := GetReposByOrg(ctx, g.Client, orgName)
	if err != nil {
		return nil, err
	}
	log.Info().Msgf("Found %d repo(s) for organization: %s", len(orgRepos), orgName)

	// Get organization members
	orgMembers, err := GetOrgMembers(ctx, g.Client, orgName)
	if err != nil {
		return nil, err
	}

	// Pre-allocate repos slice with estimated capacity
	allRepositories := make([]*github.Repository, 0, len(orgRepos)+len(orgMembers)*5)
	allRepositories = append(allRepositories, orgRepos...)

	if len(orgMembers) > 0 {
		log.Info().Msgf("Found %d public member(s) for organization: %s", len(orgMembers), orgName)
		for _, orgMember := range orgMembers {
			memberRepos, err := GetUserRepos(ctx, g.Client, orgMember.GetLogin())
			if err != nil {
				return nil, err
			}
			allRepositories = append(allRepositories, memberRepos...)
			log.Info().Msgf("Found %d repo(s) for organization member: %s", len(memberRepos), orgMember.GetLogin())
		}
	}

	// Process contributors if enabled
	if g.FindContributors {
		// Pre-allocate reconReport with estimated capacity
		reconReport = make([]ReconReport, 0, len(allRepositories)*5)
		source := fmt.Sprintf("https://github.com/orgs/%s", orgName)

		for _, repo := range allRepositories {
			repoContributors, err := GetContributors(ctx, g.Client, repo.Owner.GetLogin(), repo.GetName())
			if err != nil {
				return nil, err
			}

			for _, contributor := range repoContributors {
				r := ReconReport{
					Type:          contributor.GetType(),
					User:          contributor.GetLogin(),
					Name:          contributor.GetName(),
					Email:         contributor.GetEmail(),
					Contributions: contributor.GetContributions(),
					Source:        source,
				}

				switch r.Type {
				case "Anonymous":
					if !uniqueEmails[r.Email] {
						uniqueEmails[r.Email] = true
						reconReport = append(reconReport, r)
					}
				case "User":
					if !uniqueUsers[r.User] {
						uniqueUsers[r.User] = true
						reconReport = append(reconReport, r)
					}
				}
			}
		}
	}

	// Add the rest of org members with no contributions
	for _, m := range orgMembers {
		login := m.GetLogin()
		if login == "" {
			continue // Skip users with no login
		}

		if !uniqueUsers[login] {
			reconReport = append(reconReport, ReconReport{
				Type:          m.GetType(),
				User:          login,
				Name:          m.GetName(),
				Email:         m.GetEmail(),
				Contributions: 9999999,
				Source:        fmt.Sprintf("https://github.com/orgs/%s", orgName),
			})
		}
	}

	// Sort contributors by volume of contributions in descending order
	sort.Sort(ContributorsByContributions(reconReport))
	g.RepoResults = allRepositories
	return reconReport, g.ProcessRepos(ctx, query)
}

// EnumerateUser enumerates GitHub user repositories and contributors
// Returns a slice of ReconReport containing users and emails associated with the user's repositories
func (g *Github) EnumerateUser(ctx context.Context, user string) ([]ReconReport, error) {
	var reconReport []ReconReport
	query := fmt.Sprintf("user:%s", user)

	// Get user repositories
	userRepos, err := GetUserRepos(ctx, g.Client, user)
	if err != nil {
		return nil, err
	}
	log.Info().Msgf("Found %d repo(s) for user: %s", len(userRepos), user)

	// Process contributors if enabled
	if g.FindContributors && len(userRepos) > 0 {
		// Pre-allocate maps and slices with reasonable capacity
		usersMap := make(map[string]bool, len(userRepos)*5)
		emailsMap := make(map[string]bool, len(userRepos)*2)
		reconReport = make([]ReconReport, 0, len(userRepos)*5)
		source := fmt.Sprintf("https://github.com/%s", user)

		for _, repo := range userRepos {
			repoContributors, err := GetContributors(ctx, g.Client, repo.Owner.GetLogin(), repo.GetName())
			if err != nil {
				return nil, err
			}

			for _, contributor := range repoContributors {
				r := ReconReport{
					Type:          contributor.GetType(),
					User:          contributor.GetLogin(),
					Name:          contributor.GetName(),
					Email:         contributor.GetEmail(),
					Contributions: contributor.GetContributions(),
					Source:        source,
				}

				switch r.Type {
				case "Anonymous":
					if !emailsMap[r.Email] {
						emailsMap[r.Email] = true
						reconReport = append(reconReport, r)
					}
				case "User":
					if !usersMap[r.User] {
						usersMap[r.User] = true
						reconReport = append(reconReport, r)
					}
				}
			}
		}
	}

	// Sort contributors by volume of contributions in descending order
	sort.Sort(ContributorsByContributions(reconReport))
	g.RepoResults = userRepos
	return reconReport, g.ProcessRepos(ctx, query)
}

// EnumerateEmail enumerate commits with a specific committer email and enumerates associated repositories and contributors
// Returns a slice of ReconReport containing users and emails found in those repositories
func (g *Github) EnumerateEmail(ctx context.Context, email string) ([]ReconReport, error) {
	var reconReport []ReconReport
	emailOpts := DomainOptions{}

	if !IsValidEmail(email, emailOpts) {
		log.Error().Msgf("Skipping invalid Email address: %s", email)
	}

	query := fmt.Sprintf("committer-email:%s", email)
	repos, err := SearchCommits(ctx, g.Client, query)
	if err != nil {
		log.Error().Msgf("%s", err.Error())
	}

	// Process contributors if enabled
	if g.FindContributors && len(repos) > 0 {
		// Pre-allocate maps and slices with reasonable capacity
		usersMap := make(map[string]bool, len(repos)*5)
		emailsMap := make(map[string]bool, len(repos)*2)
		reconReport = make([]ReconReport, 0, len(repos)*5)
		source := fmt.Sprintf("https://github.com/search?q=%s&type=commits", query)

		for _, repo := range repos {
			repoName := repo.GetRepository().GetName()
			repoOwner := repo.GetRepository().GetOwner().GetLogin()

			repoContributors, err := GetContributors(ctx, g.Client, repoOwner, repoName)
			if err != nil {
				return nil, err
			}

			for _, contributor := range repoContributors {
				r := ReconReport{
					Type:          contributor.GetType(),
					User:          contributor.GetLogin(),
					Name:          contributor.GetName(),
					Email:         contributor.GetEmail(),
					Contributions: contributor.GetContributions(),
					Source:        source,
				}

				switch r.Type {
				case "Anonymous":
					if !emailsMap[r.Email] {
						emailsMap[r.Email] = true
						reconReport = append(reconReport, r)
					}
				case "User":
					if !usersMap[r.User] {
						usersMap[r.User] = true
						reconReport = append(reconReport, r)
					}
				}
			}
		}
	}

	// Sort contributors by volume of contributions in descending order
	sort.Sort(ContributorsByContributions(reconReport))
	g.CommitResults = repos
	return reconReport, g.ProcessRepos(ctx, query)
}

// EnumerateRepository enumerates GitHub repository contributors and emails leaked in commits history
// Returns a slice of ReconReport containing users and emails who contributed to the repository
func (g *Github) EnumerateRepository(ctx context.Context, owner, repo string) ([]ReconReport, error) {
	var reconReport []ReconReport
	query := fmt.Sprintf("repo:%s/%s", owner, repo)

	ghRepo, err := g.GetRepository(ctx, owner, repo)
	if err != nil {
		return nil, err
	}

	// Pre-allocate maps and slices with reasonable capacity
	usersMap := make(map[string]bool, 50)
	emailsMap := make(map[string]bool, 20)
	reconReport = make([]ReconReport, 0, 50)
	source := fmt.Sprintf("https://github.com/%s/%s", owner, repo)

	// Process contributors if enabled
	if g.FindContributors {
		repoContributors, err := GetContributors(ctx, g.Client, ghRepo.Owner.GetLogin(), ghRepo.GetName())
		if err != nil {
			return nil, err
		}

		for _, contributor := range repoContributors {
			e := ReconReport{
				Type:          contributor.GetType(),
				User:          contributor.GetLogin(),
				Name:          contributor.GetName(),
				Email:         contributor.GetEmail(),
				Contributions: contributor.GetContributions(),
				Source:        source,
			}

			switch e.Type {
			case "Anonymous":
				if !emailsMap[e.Email] {
					emailsMap[e.Email] = true
					reconReport = append(reconReport, e)
				}
			case "User":
				if !usersMap[e.User] {
					usersMap[e.User] = true
					reconReport = append(reconReport, e)
				}
			}
		}
	}

	// Find leaked emails in commits if enabled
	if g.FindLeakedEmails {
		emailOpts := DomainOptions{}
		maxCommits := 500

		// Try GraphQL first if available
		if g.GraphQLClient != nil {
			commits, err := GetRepoCommitsGraphQL(ctx, g.GraphQLClient, owner, repo, maxCommits)
			if err == nil {
				// Process commits from GraphQL
				processGraphQLCommitsForEmails(commits, source, emailsMap, &reconReport, emailOpts)
			} else {
				log.Warn().Msgf("GraphQL commit retrieval failed, falling back to REST: %v", err)

				// Fallback to REST API
				commits, err := GetRepoCommits(ctx, g.Client, owner, repo)
				if err != nil {
					return nil, err
				}

				// Process commits from REST API
				processRESTCommitsForEmails(commits, source, emailsMap, &reconReport, emailOpts)
			}
		} else {
			// GraphQL client not available, use REST API directly
			commits, err := GetRepoCommits(ctx, g.Client, owner, repo)
			if err != nil {
				return nil, err
			}

			// Process commits from REST API
			processRESTCommitsForEmails(commits, source, emailsMap, &reconReport, emailOpts)
		}
	}

	// Sort contributors by volume of contributions in descending order
	sort.Sort(ContributorsByContributions(reconReport))
	g.RepoResults = []*github.Repository{ghRepo}
	return reconReport, g.ProcessRepos(ctx, query)
}

// processRESTCommitsForEmails extracts and processes emails from REST API commit data
func processRESTCommitsForEmails(commits []*github.RepositoryCommit, source string, emailsMap map[string]bool,
	reconReport *[]ReconReport, emailOpts DomainOptions) {

	for _, commit := range commits {
		// Skip if commit or author is nil
		if commit.Commit == nil || commit.Commit.Author == nil || commit.Commit.Committer == nil {
			continue
		}

		// Process author email
		if commit.Commit.Author.Email != nil {
			extractAndStoreEmail(*commit.Commit.Author.Email, commit.Commit.Author.GetName(),
				source+"/commit/"+commit.GetSHA(), emailsMap, reconReport, emailOpts)
		}

		// Process committer email
		if commit.Commit.Committer.Email != nil {
			extractAndStoreEmail(*commit.Commit.Committer.Email, commit.Commit.Committer.GetName(),
				source+"/commit/"+commit.GetSHA(), emailsMap, reconReport, emailOpts)
		}
	}
}

// processGraphQLCommitsForEmails extracts and processes emails from GraphQL commit data
func processGraphQLCommitsForEmails(commits []CommitInfo, source string, emailsMap map[string]bool,
	reconReport *[]ReconReport, emailOpts DomainOptions) {

	for _, commit := range commits {
		// Process author email
		if commit.AuthorEmail != "" {
			extractAndStoreEmail(commit.AuthorEmail, commit.AuthorName,
				source+"/commit/"+commit.SHA, emailsMap, reconReport, emailOpts)
		}

		// Process committer email
		if commit.CommitterEmail != "" {
			extractAndStoreEmail(commit.CommitterEmail, commit.CommitterName,
				source+"/commit/"+commit.SHA, emailsMap, reconReport, emailOpts)
		}
	}
}

// extractAndStoreEmail validates and stores an email if it meets the criteria
func extractAndStoreEmail(email, name, source string, emailsMap map[string]bool,
	reconReport *[]ReconReport, emailOpts DomainOptions) {

	if !IsValidEmail(email, emailOpts) {
		log.Error().Msgf("Skipping invalid Email address: %s", email)
		return
	}

	// Only include non-noreply emails
	if !strings.Contains(email, "@users.noreply.github.com") && email != "" && !emailsMap[email] {
		emailsMap[email] = true
		*reconReport = append(*reconReport, ReconReport{
			Type:          "Anonymous",
			Email:         email,
			Name:          name,
			Contributions: 1, // Default contribution count for leaked emails
			Source:        source,
		})
	}
}
