package core

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"

	"github.com/rs/zerolog/log"
	"gopkg.in/yaml.v3"
)

// Init reads/creates config.yaml and return the Config struct
func InitConfig(config string) *Config {
	var cfg *Config
	var configFile string

	if config != "" {
		// Expand tilde in custom config path
		expandedConfigPath, err := ExpandPath(config)
		if err != nil {
			log.Warn().Msgf("Failed to expand config path %s: %v", config, err)
			configFile = config // Fall back to original path
		} else {
			configFile = expandedConfigPath
		}
		log.Debug().Msgf("Loading custom config file from %s", configFile)
	} else {
		configFile = GetConfigFilePath("config.yaml")
	}

	if CheckFileExists(configFile) {
		buf, _ := os.ReadFile(configFile)
		cfg = &Config{}
		err := yaml.Unmarshal(buf, cfg)
		if err != nil {
			log.Fatal().Msgf("%s", err.Error())
		}
	} else {
		if config != "" {
			log.Error().Msgf("Unable to load custom config file")
			os.Exit(1)
		}
		cfg = &Config{}
		buf, _ := yaml.Marshal(cfg)
		err := os.WriteFile(configFile, buf, 0644)
		if err != nil {
			log.Fatal().Msgf("Failed to create config file: %s", err.Error())
		}
	}
	return cfg
}

// GetConfigFilePath returns full path of a config file
func GetConfigFilePath(f string) string {
	configDir, err := GetDefaultConfigDir()
	if err != nil {
		log.Error().Msgf("Failed to get the default config directory")
		os.Exit(1)
	}
	return filepath.Join(configDir, f)
}

// GetDefaultConfigDir returns the default config directory
func GetDefaultConfigDir() (dir string, err error) {
	// First check if config files exist in the executable directory
	execDir, execErr := GetExecutableDir()
	if execErr == nil {
		cfgPath := filepath.Join(execDir, "config.yaml")
		dbPath := filepath.Join(execDir, "octopi.db")

		// If either config.yaml or octopi.db exists in the executable directory, use it
		if CheckFileExists(cfgPath) || CheckFileExists(dbPath) {
			return execDir, nil
		}
	}

	// Otherwise use the standard config directory logic
	if env, ok := os.LookupEnv("OCTOPI_CONFIG_DIR"); ok {
		dir = env
	} else if runtime.GOOS == "windows" {
		dir = os.Getenv("APPDATA")
		if dir == "" {
			dir = filepath.Join(os.Getenv("USERPROFILE"), "Application Data", "octopi")
		}
		dir = filepath.Join(dir, "octopi")
	} else {
		dir = filepath.Join(os.Getenv("HOME"), ".octopi")
	}
	if err := os.MkdirAll(dir, 0o775); err != nil {
		return "", fmt.Errorf("cannot create config directory: %v", err)
	}
	return dir, nil
}

// CheckFileExists checks if file exists
func CheckFileExists(path string) bool {
	_, err := os.Stat(path)
	if err == nil {
		return true
	}
	if os.IsNotExist(err) {
		return false
	}
	return false
}
