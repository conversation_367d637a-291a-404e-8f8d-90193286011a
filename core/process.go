package core

import (
	"context"
	"fmt"

	"github.com/rs/zerolog/log"
)

// ProcessCodeResults processes GitHub code search results and converts them to Octopi structs
// It filters out repositories based on fork status and size limits
// Returns a slice of Octopi and any error encountered
func (g *Github) ProcessCodeResults(ctx context.Context, query string) ([]Octopi, error) {
	var octoResults []Octopi

	for _, repo := range g.CodeResults {
		repoFork := repo.Repository.GetFork()
		if !g.IncludeForks && repoFork {
			continue
		}
		repoName := repo.Repository.GetName()
		repoURL := repo.Repository.GetHTMLURL()
		repoOwner := repo.Repository.Owner.GetLogin()
		repoSize, err := g.GetRepoSize(ctx, repoOwner, repoName)
		if err != nil {
			return nil, fmt.Errorf("failed to get code result repo size: %s", err.<PERSON>rror())
		}
		if g.MaxRepoSize > 0 && repoSize > g.MaxRepoSize*1024 {
			log.Debug().Msgf("Skipping '%s/%s' as it exceeds the maximum allowed repo size: %d > %d MB", repoOwner, repoName, repoSize/1024, g.MaxRepoSize)
			continue
		}
		octoResults = append(octoResults, Octopi{
			RepoName: repoName,
			RepoURL:  repoURL,
			RepoSize: repoSize,
			Owner:    repoOwner,
			Query:    query,
		})
	}
	return octoResults, nil
}

// ProcessCommitResults processes GitHub commit search results and converts them to Octopi structs
// It filters out repositories based on fork status and size limits
// Returns a slice of Octopi and any error encountered
func (g *Github) ProcessCommitResults(ctx context.Context, query string) ([]Octopi, error) {
	var octoResults []Octopi

	for _, repo := range g.CommitResults {
		repoFork := repo.Repository.GetFork()
		if !g.IncludeForks && repoFork {
			continue
		}
		repoName := repo.Repository.GetName()
		repoURL := repo.Repository.GetHTMLURL()
		repoOwner := repo.Repository.Owner.GetLogin()
		repoSize, err := g.GetRepoSize(ctx, repoOwner, repoName)
		if err != nil {
			return nil, fmt.Errorf("failed to get commit result repo size: %s", err.Error())
		}
		if g.MaxRepoSize > 0 && repoSize > g.MaxRepoSize*1024 {
			log.Debug().Msgf("Skipping '%s/%s' as it exceeds the maximum allowed repo size: %d > %d MB", repoOwner, repoName, repoSize/1024, g.MaxRepoSize)
			continue
		}
		octoResults = append(octoResults, Octopi{
			RepoName: repoName,
			RepoURL:  repoURL,
			RepoSize: repoSize,
			Owner:    repoOwner,
			Query:    query,
		})
	}
	return octoResults, nil
}

// ProcessRepoResults processes GitHub repository search results and converts them to Octopi structs
// It filters out repositories based on fork status and size limits
// Returns a slice of Octopi and any error encountered
func (g *Github) ProcessRepoResults(ctx context.Context, query string) ([]Octopi, error) {
	var octoResults []Octopi

	for _, repo := range g.RepoResults {
		repoFork := repo.GetFork()
		if !g.IncludeForks && repoFork {
			continue
		}
		repoName := repo.GetName()
		repoURL := repo.GetHTMLURL()
		repoOwner := repo.Owner.GetLogin()
		repoSize := repo.GetSize()
		if g.MaxRepoSize > 0 && repoSize > g.MaxRepoSize*1024 {
			log.Debug().Msgf("Skipping '%s/%s' as it exceeds the maximum allowed repo size: %d > %d MB", repoOwner, repoName, repoSize/1024, g.MaxRepoSize)
			continue
		}
		octoResults = append(octoResults, Octopi{
			RepoName: repoName,
			RepoURL:  repoURL,
			RepoSize: repoSize,
			Owner:    repoOwner,
			Query:    query,
		})
	}
	return octoResults, nil
}

// ProcessRepos combines and deduplicates results from repository, code, and commit searches
// It processes all available search results and adds them to the database
// Returns an error if any processing step fails
func (g *Github) ProcessRepos(ctx context.Context, query string) error {
	var octoResults []Octopi
	seen := make(map[string]bool)

	if len(g.RepoResults) > 0 {
		repos, err := g.ProcessRepoResults(ctx, query)
		if err != nil {
			return err
		}
		for _, repo := range repos {
			if !seen[repo.RepoURL] {
				seen[repo.RepoURL] = true
			} else {
				continue
			}
			octoResults = append(octoResults, repo)
		}
	}
	if len(g.CodeResults) > 0 {
		repos, err := g.ProcessCodeResults(ctx, query)
		if err != nil {
			return err
		}
		for _, repo := range repos {
			if !seen[repo.RepoURL] {
				seen[repo.RepoURL] = true
			} else {
				continue
			}
			octoResults = append(octoResults, repo)
		}
	}
	if len(g.CommitResults) > 0 {
		repos, err := g.ProcessCommitResults(ctx, query)
		if err != nil {
			return err
		}
		for _, repo := range repos {
			if !seen[repo.RepoURL] {
				seen[repo.RepoURL] = true
			} else {
				continue
			}
			octoResults = append(octoResults, repo)
		}
	}

	return AddOctos(octoResults)
}
