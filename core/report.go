package core

import (
	"encoding/csv"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/rs/zerolog/log"
)

// GetReportFile returns the path to the report file for the given reporter
func GetReportFile(reporter Reporter) string {
	return GetReportFileWithFormat(reporter, "json")
}

// GetReportFileWithFormat returns the path to the report file for the given reporter and format
func GetReportFileWithFormat(reporter Reporter, format string) string {
	var reportFile string
	timeFormat := time.Now().Format("2006-01-02-15-04-05")

	// Determine file extension based on format
	var ext string
	switch strings.ToLower(format) {
	case "json":
		ext = "jsonl"
	case "csv":
		ext = "csv"
	case "markdown", "md":
		ext = "md"
	case "table":
		ext = "txt"
	default:
		ext = "txt"
	}

	switch reporter {
	case Reconnaissance:
		reportFile = fmt.Sprintf("recon-%s.%s", timeFormat, ext)
	case DanglerScanner:
		reportFile = fmt.Sprintf("dangler-%s.%s", timeFormat, ext)
	case GitleaksScanner:
		reportFile = fmt.Sprintf("gitleaks-%s.%s", timeFormat, ext)
	case TrufflehogScanner:
		reportFile = fmt.Sprintf("trufflehog-%s.%s", timeFormat, ext)
	case GitleaksSensitive:
		reportFile = fmt.Sprintf("sensitive-leaks-%s.%s", timeFormat, ext)
	case TrufflehogVerified:
		reportFile = fmt.Sprintf("verified-leaks-%s.%s", timeFormat, ext)
	}
	reportFile = filepath.Join(GetReportsPath(), reportFile)
	return reportFile
}

// GetReportsPath returns the path to the reports directory
func GetReportsPath() string {
	configDir, err := GetDefaultConfigDir()
	if err != nil {
		log.Error().Msgf("Failed to get the default config directory")
	}
	reportsDir := filepath.Join(configDir, "reports")
	if err := os.MkdirAll(reportsDir, 0o775); err != nil {
		log.Error().Msgf("cannot create reports directory: %s", err.Error())
	}
	return reportsDir
}

// WriteJSONReport writes a JSON report to a file
func WriteJSONReport(v any, reportFile string) error {
	file, err := os.OpenFile(reportFile, os.O_CREATE|os.O_APPEND|os.O_WRONLY, 0644)
	if err != nil {
		return err
	}
	defer file.Close()
	encoder := json.NewEncoder(file)
	encoder.SetIndent("", " ")
	encoder.SetEscapeHTML(false)
	return encoder.Encode(v)
}

// SaveReconReport saves the reconnaissance report to a file in the specified format
func SaveReconReport(reconReport []ReconReport) error {
	return SaveReconReportWithFormat(reconReport, "json")
}

// SaveReconReportWithFormat saves the reconnaissance report to a file in the specified format
func SaveReconReportWithFormat(reconReport []ReconReport, format string) error {
	var anonEnumTotal int
	var userEnumTotal int

	// Count the different types for logging
	for _, r := range reconReport {
		if r.Type == "Anonymous" {
			anonEnumTotal++
		}
		if r.Type == "User" {
			userEnumTotal++
		}
	}

	reportFile := GetReportFileWithFormat(Reconnaissance, format)

	// Save based on format
	switch strings.ToLower(format) {
	case "json":
		return saveReconReportJSON(reconReport, reportFile, anonEnumTotal, userEnumTotal)
	case "csv":
		return saveReconReportCSV(reconReport, reportFile, anonEnumTotal, userEnumTotal)
	case "markdown", "md":
		return saveReconReportMarkdown(reconReport, reportFile, anonEnumTotal, userEnumTotal)
	case "table":
		return saveReconReportTable(reconReport, reportFile, anonEnumTotal, userEnumTotal)
	default:
		return fmt.Errorf("unsupported output format: %s", format)
	}
}

// saveReconReportJSON saves the report in JSON format (original behavior)
func saveReconReportJSON(reconReport []ReconReport, reportFile string, anonTotal, userTotal int) error {
	for _, r := range reconReport {
		if r.Type == "Anonymous" || r.Type == "User" {
			err := WriteJSONReport(r, reportFile)
			if err != nil {
				return fmt.Errorf("failed to save reconnaissance report: %s", err.Error())
			}
		}
	}
	log.Info().Msgf("Reconnaissance found %d Anonymous and %d User contributors", anonTotal, userTotal)
	log.Info().Msgf("JSON report saved to: %s", reportFile)
	return nil
}

// saveReconReportCSV saves the report in CSV format
func saveReconReportCSV(reconReport []ReconReport, reportFile string, anonTotal, userTotal int) error {
	content, err := FormatReconReportAsCSV(reconReport)
	if err != nil {
		return fmt.Errorf("failed to format CSV: %s", err.Error())
	}

	err = os.WriteFile(reportFile, []byte(content), 0644)
	if err != nil {
		return fmt.Errorf("failed to write CSV report: %s", err.Error())
	}

	log.Info().Msgf("Reconnaissance found %d Anonymous and %d User contributors", anonTotal, userTotal)
	log.Info().Msgf("CSV report saved to: %s", reportFile)
	return nil
}

// saveReconReportMarkdown saves the report in Markdown format
func saveReconReportMarkdown(reconReport []ReconReport, reportFile string, anonTotal, userTotal int) error {
	content, err := FormatReconReportAsMarkdown(reconReport)
	if err != nil {
		return fmt.Errorf("failed to format Markdown: %s", err.Error())
	}

	err = os.WriteFile(reportFile, []byte(content), 0644)
	if err != nil {
		return fmt.Errorf("failed to write Markdown report: %s", err.Error())
	}

	log.Info().Msgf("Reconnaissance found %d Anonymous and %d User contributors", anonTotal, userTotal)
	log.Info().Msgf("Markdown report saved to: %s", reportFile)
	return nil
}

// saveReconReportTable saves the report in table format
func saveReconReportTable(reconReport []ReconReport, reportFile string, anonTotal, userTotal int) error {
	content, err := FormatReconReportAsTable(reconReport)
	if err != nil {
		return fmt.Errorf("failed to format table: %s", err.Error())
	}

	err = os.WriteFile(reportFile, []byte(content), 0644)
	if err != nil {
		return fmt.Errorf("failed to write table report: %s", err.Error())
	}

	log.Info().Msgf("Reconnaissance found %d Anonymous and %d User contributors", anonTotal, userTotal)
	log.Info().Msgf("Table report saved to: %s", reportFile)
	return nil
}

// FormatReconReportAsCSV formats the reports as CSV
func FormatReconReportAsCSV(reports []ReconReport) (string, error) {
	var buf strings.Builder
	writer := csv.NewWriter(&buf)

	// Write header
	header := []string{"Type", "User", "Name", "Email", "Contributions", "Source"}
	if err := writer.Write(header); err != nil {
		return "", err
	}

	// Write data
	for _, report := range reports {
		record := []string{
			report.Type,
			report.User,
			report.Name,
			report.Email,
			fmt.Sprintf("%d", report.Contributions),
			report.Source,
		}
		if err := writer.Write(record); err != nil {
			return "", err
		}
	}

	writer.Flush()
	if err := writer.Error(); err != nil {
		return "", err
	}

	return buf.String(), nil
}

// FormatReconReportAsMarkdown formats the reports as Markdown table
func FormatReconReportAsMarkdown(reports []ReconReport) (string, error) {
	var buf strings.Builder

	buf.WriteString("# Reconnaissance Results\n\n")
	buf.WriteString("| Type | User | Name | Email | Contributions | Source |\n")
	buf.WriteString("|------|------|------|-------|---------------|--------|\n")

	for _, report := range reports {
		buf.WriteString(fmt.Sprintf("| %s | %s | %s | %s | %d | %s |\n",
			report.Type,
			report.User,
			report.Name,
			report.Email,
			report.Contributions,
			report.Source,
		))
	}

	return buf.String(), nil
}

// FormatReconReportAsTable formats the reports as a simple table
func FormatReconReportAsTable(reports []ReconReport) (string, error) {
	var buf strings.Builder

	// Header
	buf.WriteString(fmt.Sprintf("%-12s %-20s %-25s %-30s %-13s %s\n",
		"TYPE", "USER", "NAME", "EMAIL", "CONTRIBUTIONS", "SOURCE"))
	buf.WriteString(strings.Repeat("-", 120) + "\n")

	// Data
	for _, report := range reports {
		buf.WriteString(fmt.Sprintf("%-12s %-20s %-25s %-30s %-13d %s\n",
			truncateString(report.Type, 12),
			truncateString(report.User, 20),
			truncateString(report.Name, 25),
			truncateString(report.Email, 30),
			report.Contributions,
			report.Source,
		))
	}

	buf.WriteString(fmt.Sprintf("\nTotal results: %d\n", len(reports)))
	return buf.String(), nil
}

// truncateString truncates a string to the specified length
func truncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	if maxLen <= 3 {
		return s[:maxLen]
	}
	return s[:maxLen-3] + "..."
}
