package core

import (
	"bytes"
	"context"
	"fmt"
	"net/url"
	"os"
	"os/exec"
	"strings"

	"github.com/go-git/go-git/v5"
	"github.com/go-git/go-git/v5/plumbing"
	"github.com/rs/zerolog/log"
)

// GetLatestGitCommit returns latest commit in repository (across all branches)
func GetLatestGitCommit(clonePath string, gitRepo *git.Repository) (*Commit, error) {
	gitArgs := []string{
		"-C", clonePath, "for-each-ref",
		"--sort=-committerdate", "refs/heads/",
		"--count=1", "--format=%(objectname)",
	}
	cmd := exec.Command(GetCommandPath("git"), gitArgs...)
	log.Debug().Msgf("Executing git command: %s", cmd.String())

	outputBytes, err := cmd.CombinedOutput()
	output := string(outputBytes)
	if err != nil {
		err = fmt.Errorf("error executing git: %w, %s", err, output)
	}
	if output != "" {
		log.Debug().Msgf("Git command finished: %s", output)
	}

	if cmd.ProcessState == nil {
		return nil, fmt.Errorf("git command exited with no output")
	} else if cmd.ProcessState.ExitCode() != 0 {
		log.Debug().Msgf("git failed: %s", err)
		return nil, fmt.Errorf("could not get latest commit: %w", err)
	}

	hash := strings.TrimSpace(strings.TrimSuffix(output, "\n"))

	if hash == "" {
		return nil, fmt.Errorf("probably an empty repository")
	}
	isValid := plumbing.IsHash(hash)
	if isValid != true {
		return nil, fmt.Errorf("commit SHA hash is not valid: %s", hash)
	}

	commit, err := gitRepo.CommitObject(plumbing.NewHash(hash))
	if err != nil {
		return nil, fmt.Errorf("commit SHA hash not found: %w", err)
	}

	latestCommit := &Commit{
		SHA:   hash,
		User:  commit.Committer.Name,
		Email: commit.Committer.Email,
		Date:  commit.Committer.When.UTC(),
	}
	return latestCommit, nil
}

// MakeGitRef creates a git reference
func MakeGitRef(r *git.Repository, name, sha string) error {
	ref := plumbing.NewHashReference(plumbing.ReferenceName(name), plumbing.NewHash(sha))
	return r.Storer.SetReference(ref)
}

// RemoveGetReferences removes a list of git references
func RemoveGetReferences(r *git.Repository, names []string) error {
	for _, name := range names {
		err := r.Storer.RemoveReference(plumbing.ReferenceName(name))
		if err != nil {
			return err
		}
	}
	return nil
}

// CreateGitRef creates a git reference for a dangling commit
func CreateGitRef(repo *git.Repository, sha string) error {
	ref := fmt.Sprintf("refs/dangling/%s", sha)
	if err := MakeGitRef(repo, ref, sha); err != nil {
		return fmt.Errorf("failed to create ref for dangling commit: %s", sha)
	}
	return nil
}

// RepoFromPath opens a git repository from a given path
func RepoFromPath(path string, isBare bool) (*git.Repository, error) {
	options := &git.PlainOpenOptions{}
	if !isBare {
		options.DetectDotGit = true
		options.EnableDotGitCommonDir = true
	}
	return git.PlainOpenWithOptions(path, options)
}

// FetchDanglingCommit fetches a dangling commit from a remote repository and checks it out into a new branch
func FetchDanglingCommit(repoPath string, commit string) error {
	log.Debug().Msgf("Fetching dangling commit %s", commit)

	// Fetch commit from Github
	gitArgs := []string{
		"-C", repoPath,
		"--work-tree", repoPath,
		"fetch",
		"--quiet",
		"--no-auto-gc",
		"origin", commit,
	}
	_, err := runGitCommand(gitArgs)
	if err != nil {
		return fmt.Errorf("failed to fetch dangling commit %s: %w", commit, err)
	}

	// Checkout the commit into a new branch
	branchName := fmt.Sprintf("dangling_%s", commit)
	gitArgs = []string{
		"-C", repoPath,
		"--work-tree", repoPath,
		"checkout",
		"--quiet",
		"-b", branchName,
		commit,
	}
	_, err = runGitCommand(gitArgs)
	if err != nil {
		return fmt.Errorf("failed to checkout dangling commit %s to branch %s: %w",
			commit, branchName, err)
	}
	return nil
}

// GetCurrentBranch returns the name of the current branch
func GetCurrentBranch(repoPath string) (string, error) {
	gitArgs := []string{
		"-C", repoPath,
		"branch", "--show-current",
	}
	output, err := runGitCommand(gitArgs)
	if err != nil {
		return "", fmt.Errorf("failed to get current branch: %w", err)
	}
	return strings.TrimSpace(string(output)), nil
}

// CheckoutBranch checks out the specified branch
func CheckoutBranch(repoPath string, branch string) error {
	gitArgs := []string{
		"-C", repoPath,
		"--work-tree", repoPath,
		"checkout",
		"--quiet",
		branch,
	}
	_, err := runGitCommand(gitArgs)
	if err != nil {
		return fmt.Errorf("failed to checkout branch %s: %w", branch, err)
	}
	return nil
}

// runGitCommand executes a git command and returns the output and error
func runGitCommand(args []string) ([]byte, error) {
	cmd := exec.Command(GetCommandPath("git"), args...)
	cmd.Env = os.Environ()
	cmd.Env = append(cmd.Env, "GIT_TERMINAL_PROMPT=0")

	log.Debug().Msgf("Executing git command: %s", cmd.String())
	outputBytes, err := cmd.CombinedOutput()
	output := string(outputBytes)
	if err != nil {
		err = fmt.Errorf("error executing git command: %w, %s", err, output)
	}
	if output != "" {
		log.Debug().Msgf("Git command finished: %s", output)
	}
	if cmd.ProcessState == nil {
		err = fmt.Errorf("git fetch command exited with no output")
	} else if cmd.ProcessState.ExitCode() != 0 {
		err = fmt.Errorf("git command exited with non-zero exit code: %s", err)
	}
	return outputBytes, err
}

// GitURLParse parses a Git URL, handling both SSH and HTTPS formats
func GitURLParse(gitURL string) (*url.URL, error) {
	parsedURL, originalError := url.Parse(gitURL)
	if originalError != nil {
		var err error
		gitURLBytes := []byte("ssh://" + gitURL)
		colonIndex := bytes.LastIndex(gitURLBytes, []byte(":"))
		gitURLBytes[colonIndex] = byte('/')
		parsedURL, err = url.Parse(string(gitURLBytes))
		if err != nil {
			return nil, originalError
		}
	}
	return parsedURL, nil
}

// cloneParams encapsulates the parameters required for cloning a Git repository
type cloneParams struct {
	gitURL             string        // The Git URL to clone from
	args               []string      // Additional arguments for the git clone command
	clonePath          string        // The local path to clone the repository to
	userInfo           *url.Userinfo // Optional user information for authentication
	skipAdditionalRefs bool          // Whether to skip fetching additional Git references
}

// CloneRepo orchestrates the cloning of a given Git repository, returning its local path
// and a git.Repository object for further operations. The function sets up error handling
// infrastructure, ensuring that any encountered errors trigger a cleanup of resources.
// The core cloning logic is delegated to a nested function, which returns errors to the
// outer function for centralized error handling and cleanup.
func CloneRepo(ctx context.Context, gitURL string, userInfo *url.Userinfo, skipAdditionalRefs bool, args ...string) (string, *git.Repository, error) {
	clonePath, err := MakeTempDir()
	if err != nil {
		return "", nil, err
	}

	repo, err := executeClone(cloneParams{gitURL, args, clonePath, userInfo, skipAdditionalRefs})
	if err != nil {
		return "", nil, err
	}

	return clonePath, repo, nil
}

// stripPassword removes the password from a Git URL, returning the modified URL and the password
func stripPassword(u string) (string, string, error) {
	if strings.HasPrefix(u, "git@") {
		return u, "", nil
	}

	repoURL, err := url.Parse(u)
	if err != nil {
		return "", "", fmt.Errorf("repo remote is not a URI: %w", err)
	}

	password, _ := repoURL.User.Password()
	repoURL.User = nil

	return repoURL.String(), password, nil
}

// executeClone prepares the Git URL, constructs, and executes the git clone command using the provided
// clonePath. It then opens the cloned repository, returning a git.Repository object.
func executeClone(params cloneParams) (*git.Repository, error) {
	cloneURL, err := GitURLParse(params.gitURL)
	if err != nil {
		return nil, err
	}
	if cloneURL.User == nil {
		cloneURL.User = params.userInfo
	}

	gitArgs := []string{
		"clone",
		cloneURL.String(),
		params.clonePath,
		"--quiet",
	}

	if !params.skipAdditionalRefs {
		gitArgs = append(gitArgs,
			"-c",
			"remote.origin.fetch=+refs/*:refs/remotes/origin/*")
	}

	gitArgs = append(gitArgs, params.args...)
	cloneCmd := exec.Command(GetCommandPath("git"), gitArgs...)
	cloneCmd.Env = os.Environ()
	cloneCmd.Env = append(cloneCmd.Env, "GIT_TERMINAL_PROMPT=0")

	safeURL, secretForRedaction, err := stripPassword(params.gitURL)
	if err != nil {
		log.Info().Msgf("error stripping password from git url: %s", err)
	}
	outputBytes, err := cloneCmd.CombinedOutput()
	var output string
	if secretForRedaction != "" {
		output = strings.ReplaceAll(string(outputBytes), secretForRedaction, "<secret>")
	} else {
		output = string(outputBytes)
	}

	if err != nil {
		err = fmt.Errorf("error executing git clone: %w, %s", err, output)
	}

	if cloneCmd.ProcessState == nil {
		return nil, fmt.Errorf("clone command exited with no output")
	} else if cloneCmd.ProcessState.ExitCode() != 0 {
		log.Info().Msgf("git clone failed: %s", err)
		return nil, fmt.Errorf("could not clone repo: %s, %w", safeURL, err)
	}

	options := &git.PlainOpenOptions{DetectDotGit: true, EnableDotGitCommonDir: true}
	repo, err := git.PlainOpenWithOptions(params.clonePath, options)
	if err != nil {
		return nil, fmt.Errorf("could not open cloned repo: %w", err)
	}

	return repo, nil
}

// CloneRepoUsingToken clones a repo using a provided token.
func CloneRepoUsingToken(ctx context.Context, token, gitUrl, user string, skipAdditionalRefs bool, args ...string) (string, *git.Repository, error) {
	userInfo := url.UserPassword(user, token)
	return CloneRepo(ctx, gitUrl, userInfo, skipAdditionalRefs, args...)
}

// CloneRepoUsingUnauthenticated clones a repo with no authentication required.
func CloneRepoUsingUnauthenticated(ctx context.Context, url string, skipAdditionalRefs bool, args ...string) (string, *git.Repository, error) {
	// Normalize the repository URL first
	normalizedURL, err := NormalizeGitHubURL(url)
	if err != nil {
		return "", nil, err
	}

	return CloneRepo(ctx, normalizedURL, nil, skipAdditionalRefs, args...)
}

// CloneRepoUsingSSH clones a repo using SSH.
func CloneRepoUsingSSH(ctx context.Context, gitURL string, skipAdditionalRefs bool, args ...string) (string, *git.Repository, error) {
	userInfo := url.User("git")
	return CloneRepo(ctx, gitURL, userInfo, skipAdditionalRefs, args...)
}

// IsCommitAccessibleLocally checks if a commit is reachable from any branch or tag
// Returns true if the commit is reachable (regular commit), false if it's dangling or doesn't exist
func IsCommitAccessibleLocally(repoPath, commitSHA string) bool {
	// Check if commit is reachable from any branch
	gitArgs := []string{
		"-C", repoPath,
		"branch",
		"--contains", commitSHA,
	}
	outputBytes, err := runGitCommand(gitArgs)
	if err == nil && strings.TrimSpace(string(outputBytes)) != "" {
		log.Debug().Msgf("Commit %s is reachable from branches", commitSHA)
		return true
	}

	// Check if commit is reachable from any tag
	gitArgs = []string{
		"-C", repoPath,
		"tag",
		"--contains", commitSHA,
	}
	outputBytes, err = runGitCommand(gitArgs)
	if err == nil && strings.TrimSpace(string(outputBytes)) != "" {
		log.Debug().Msgf("Commit %s is reachable from tags", commitSHA)
		return true
	}

	// If not reachable from branches or tags, it's either dangling or doesn't exist
	// For Dangler purposes, both cases mean "not accessible" so return false
	log.Debug().Msgf("Commit %s is not reachable from any branch or tag", commitSHA)
	return false
}
