package core

import (
	"sync"
	"time"
)

// CacheManager handles caching for API responses
type CacheManager struct {
	sync.RWMutex
	caches     map[string]map[string]interface{}
	expiry     map[string]time.Time
	maxEntries int
	defaultTTL time.Duration
}

// NewCacheManager creates a new cache manager
func NewCacheManager(maxEntries int, defaultTTL time.Duration) *CacheManager {
	return &CacheManager{
		caches:     make(map[string]map[string]interface{}),
		expiry:     make(map[string]time.Time),
		maxEntries: maxEntries,
		defaultTTL: defaultTTL,
	}
}

// Get retrieves a value from the cache
func (c *CacheManager) Get(cacheType, key string) (interface{}, bool) {
	c.RLock()
	defer c.RUnlock()

	// Check if cache type exists
	cache, exists := c.caches[cacheType]
	if !exists {
		return nil, false
	}

	// Check if key exists
	value, exists := cache[key]
	if !exists {
		return nil, false
	}

	// Check if expired
	expiry, exists := c.expiry[key]
	if !exists || time.Now().After(expiry) {
		return nil, false
	}

	// Return value and indicate it was a cache hit
	return value, true
}

// Set stores a value in the cache
func (c *CacheManager) Set(cacheType, key string, value interface{}, ttl time.Duration) {
	c.Lock()
	defer c.Unlock()

	// Initialize cache type if it doesn't exist
	if _, exists := c.caches[cacheType]; !exists {
		c.caches[cacheType] = make(map[string]interface{})
	}

	// Check if we need to clean up before adding
	if len(c.expiry) >= c.maxEntries {
		c.cleanupExpiredLocked()

		// If still too many entries, remove oldest
		if len(c.expiry) >= c.maxEntries {
			c.removeOldestLocked()
		}
	}

	// Set the value and expiry
	c.caches[cacheType][key] = value

	if ttl <= 0 {
		ttl = c.defaultTTL
	}
	c.expiry[key] = time.Now().Add(ttl)
}

// Delete removes a value from the cache
func (c *CacheManager) Delete(key string) {
	c.Lock()
	defer c.Unlock()

	// Remove from all caches
	for _, cache := range c.caches {
		delete(cache, key)
	}

	// Remove expiry
	delete(c.expiry, key)
}

// CleanupExpired removes all expired entries from the cache
func (c *CacheManager) CleanupExpired() {
	c.Lock()
	defer c.Unlock()

	c.cleanupExpiredLocked()
}

// cleanupExpiredLocked removes expired entries (assumes lock is held)
func (c *CacheManager) cleanupExpiredLocked() {
	now := time.Now()

	for key, expiry := range c.expiry {
		if now.After(expiry) {
			// Remove from all caches
			for _, cache := range c.caches {
				delete(cache, key)
			}

			// Remove expiry
			delete(c.expiry, key)
		}
	}
}

// removeOldestLocked removes the oldest entry (assumes lock is held)
func (c *CacheManager) removeOldestLocked() {
	var oldestKey string
	var oldestTime time.Time

	// Find oldest entry
	first := true
	for key, expiry := range c.expiry {
		if first || expiry.Before(oldestTime) {
			oldestKey = key
			oldestTime = expiry
			first = false
		}
	}

	// Remove oldest entry if found
	if oldestKey != "" {
		for _, cache := range c.caches {
			delete(cache, oldestKey)
		}
		delete(c.expiry, oldestKey)
	}
}
