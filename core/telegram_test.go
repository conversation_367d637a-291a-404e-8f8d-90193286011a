package core

import (
	"os"
	"strconv"
	"testing"
	"time"
)

func TestTelegramNotification(t *testing.T) {
	// Skip test if running in CI environment or if explicitly skipping integration tests
	if os.Getenv("CI") != "" || os.Getenv("SKIP_INTEGRATION_TESTS") != "" {
		t.Skip("Skipping integration test in CI environment")
	}

	// Get credentials from environment variables or use defaults for manual testing
	botToken := os.Getenv("TELEGRAM_BOT_TOKEN")
	if botToken == "" {
		botToken = "6012412491:AAGFg56AhNDUyWLUVF313LxH1URhnM79V8E" // Default for manual testing
		t.Log("Using default bot token. Set TELEGRAM_BOT_TOKEN env var to override.")
	}

	var chatID int64 = -1001662881987 // Default for manual testing
	if os.Getenv("TELEGRAM_CHAT_ID") != "" {
		var err error
		chatIDStr := os.Getenv("TELEGRAM_CHAT_ID")
		chatID, err = strconv.ParseInt(chatIDStr, 10, 64)
		if err != nil {
			t.Fatalf("Invalid TELEGRAM_CHAT_ID: %v", err)
		}
	} else {
		t.Log("Using default chat ID. Set TELEGRAM_CHAT_ID env var to override.")
	}

	// Create a test notification with timestamp to identify this specific test run
	timestamp := time.Now().Format("2006-01-02 15:04:05 MST")
	finding := &Notification{
		Query:     "armor-code in:login",
		Rule:      "AWS",
		Secret:    "AKIARPHQIQGW5JJRABFP",
		Commit:    "7444c459b9b9c21e41661c16506823024c5d090e",
		Author:    "davex <<EMAIL>>",
		File:      "aws-commands/commands.md",
		Line:      5,
		Timestamp: timestamp, // Use current time to identify this test run
		RepoURL:   "https://github.com/armor-code/aws",
	}

	// Send the actual notification
	t.Logf("Sending real Telegram notification with timestamp: %s", timestamp)
	err := SendTelegram(botToken, chatID, *finding)

	// Check for errors
	if err != nil {
		t.Errorf("SendTelegram returned an error: %v", err)
	} else {
		t.Logf("Successfully sent Telegram notification. Please check the chat %d for the message.", chatID)
	}

	// Note: In a real integration test, you might want to also verify the message was received
	// This would require a bot that can read messages from the chat, which is beyond the scope
	// of this simple test. For now, manual verification is required.
}
