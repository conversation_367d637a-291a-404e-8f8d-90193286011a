package core

import (
	"context"
	"fmt"
	"time"

	"github.com/shurcooL/githubv4"
)

// CommitInfo contains detailed information about a commit
type CommitInfo struct {
	SHA            string
	Message        string
	CommittedDate  time.Time
	AuthorName     string
	AuthorEmail    string
	CommitterName  string
	CommitterEmail string
}

// RepositoryInfo contains information about a GitHub repository
type RepositoryInfo struct {
	ID              string
	Name            string
	FullName        string
	Description     string
	URL             string
	CreatedAt       time.Time
	UpdatedAt       time.Time
	PushedAt        time.Time
	IsPrivate       bool
	IsFork          bool
	IsArchived      bool
	DefaultBranch   string
	ForkCount       int
	StargazerCount  int
	Owner           OwnerInfo
	License         string
	PrimaryLanguage string
}

// OwnerInfo contains information about a repository owner
type OwnerInfo struct {
	Login string
	URL   string
}

// ContributorInfo contains information about a repository contributor
type ContributorInfo struct {
	Login     string
	Name      string
	Email     string
	AvatarURL string
	URL       string
	Bio       string
	Company   string
	Location  string
}

// GetRepositoryGraphQL retrieves a GitHub repository using GraphQL API
// This is more efficient than the REST API for getting basic repository information
func GetRepositoryGraphQL(ctx context.Context, client *githubv4.C<PERSON>, owner, repo string) (*RepositoryInfo, error) {
	var query struct {
		Repository struct {
			ID               githubv4.ID
			Name             githubv4.String
			NameWithOwner    githubv4.String
			Description      githubv4.String
			URL              githubv4.URI
			CreatedAt        githubv4.DateTime
			UpdatedAt        githubv4.DateTime
			PushedAt         githubv4.DateTime
			IsPrivate        githubv4.Boolean
			IsFork           githubv4.Boolean
			IsArchived       githubv4.Boolean
			ForkCount        githubv4.Int
			StargazerCount   githubv4.Int
			DefaultBranchRef struct {
				Name githubv4.String
			}
			Owner struct {
				Login githubv4.String
				URL   githubv4.URI
			}
			LicenseInfo struct {
				Name githubv4.String
			}
			PrimaryLanguage struct {
				Name githubv4.String
			}
		} `graphql:"repository(owner: $owner, name: $name)"`
	}

	variables := map[string]interface{}{
		"owner": githubv4.String(owner),
		"name":  githubv4.String(repo),
	}

	err := client.Query(ctx, &query, variables)
	if err != nil {
		return nil, fmt.Errorf("GraphQL query failed: %w", err)
	}

	repository := query.Repository
	repoInfo := &RepositoryInfo{
		ID:             fmt.Sprintf("%v", repository.ID),
		Name:           string(repository.Name),
		FullName:       string(repository.NameWithOwner),
		Description:    string(repository.Description),
		URL:            string(repository.URL.String()),
		CreatedAt:      repository.CreatedAt.Time,
		UpdatedAt:      repository.UpdatedAt.Time,
		PushedAt:       repository.PushedAt.Time,
		IsPrivate:      bool(repository.IsPrivate),
		IsFork:         bool(repository.IsFork),
		IsArchived:     bool(repository.IsArchived),
		DefaultBranch:  string(repository.DefaultBranchRef.Name),
		ForkCount:      int(repository.ForkCount),
		StargazerCount: int(repository.StargazerCount),
		Owner: OwnerInfo{
			Login: string(repository.Owner.Login),
			URL:   string(repository.Owner.URL.String()),
		},
		License:         string(repository.LicenseInfo.Name),
		PrimaryLanguage: string(repository.PrimaryLanguage.Name),
	}

	return repoInfo, nil
}

// GetUserRepositoriesGraphQL retrieves repositories for a GitHub user using GraphQL
// This is more efficient than the REST API for getting multiple repositories at once
func GetUserRepositoriesGraphQL(ctx context.Context, client *githubv4.Client, username string, maxRepos int) ([]RepositoryInfo, error) {
	var query struct {
		User struct {
			Repositories struct {
				Nodes []struct {
					ID               githubv4.ID
					Name             githubv4.String
					NameWithOwner    githubv4.String
					Description      githubv4.String
					URL              githubv4.URI
					CreatedAt        githubv4.DateTime
					UpdatedAt        githubv4.DateTime
					PushedAt         githubv4.DateTime
					IsPrivate        githubv4.Boolean
					IsFork           githubv4.Boolean
					IsArchived       githubv4.Boolean
					ForkCount        githubv4.Int
					StargazerCount   githubv4.Int
					DefaultBranchRef struct {
						Name githubv4.String
					}
					Owner struct {
						Login githubv4.String
						URL   githubv4.URI
					}
					PrimaryLanguage struct {
						Name githubv4.String
					}
				}
				PageInfo struct {
					HasNextPage bool
					EndCursor   githubv4.String
				}
			} `graphql:"repositories(first: $first, after: $cursor, orderBy: {field: UPDATED_AT, direction: DESC})"`
		} `graphql:"user(login: $username)"`
	}

	var repos []RepositoryInfo
	variables := map[string]interface{}{
		"username": githubv4.String(username),
		"first":    githubv4.Int(100),
		"cursor":   (*githubv4.String)(nil),
	}

	for {
		err := client.Query(ctx, &query, variables)
		if err != nil {
			return nil, fmt.Errorf("GraphQL query failed: %w", err)
		}

		for _, repo := range query.User.Repositories.Nodes {
			repos = append(repos, RepositoryInfo{
				ID:             fmt.Sprintf("%v", repo.ID),
				Name:           string(repo.Name),
				FullName:       string(repo.NameWithOwner),
				Description:    string(repo.Description),
				URL:            string(repo.URL.String()),
				CreatedAt:      repo.CreatedAt.Time,
				UpdatedAt:      repo.UpdatedAt.Time,
				PushedAt:       repo.PushedAt.Time,
				IsPrivate:      bool(repo.IsPrivate),
				IsFork:         bool(repo.IsFork),
				IsArchived:     bool(repo.IsArchived),
				DefaultBranch:  string(repo.DefaultBranchRef.Name),
				ForkCount:      int(repo.ForkCount),
				StargazerCount: int(repo.StargazerCount),
				Owner: OwnerInfo{
					Login: string(repo.Owner.Login),
					URL:   string(repo.Owner.URL.String()),
				},
				PrimaryLanguage: string(repo.PrimaryLanguage.Name),
			})

			if maxRepos > 0 && len(repos) >= maxRepos {
				return repos, nil
			}
		}

		if !query.User.Repositories.PageInfo.HasNextPage {
			break
		}
		variables["cursor"] = githubv4.NewString(query.User.Repositories.PageInfo.EndCursor)
	}

	return repos, nil
}

// GetContributorsGraphQL retrieves contributors for a GitHub repository using GraphQL
// This is more efficient than the REST API for getting contributor information
func GetContributorsGraphQL(ctx context.Context, client *githubv4.Client, owner, repo string) ([]ContributorInfo, error) {
	var query struct {
		Repository struct {
			Collaborators struct {
				Nodes []struct {
					Login     githubv4.String
					Name      githubv4.String
					Email     githubv4.String
					AvatarURL githubv4.URI
					URL       githubv4.URI
					Bio       githubv4.String
					Company   githubv4.String
					Location  githubv4.String
				}
				PageInfo struct {
					HasNextPage bool
					EndCursor   githubv4.String
				}
			} `graphql:"collaborators(first: $first, after: $cursor)"`
		} `graphql:"repository(owner: $owner, name: $name)"`
	}

	var contributors []ContributorInfo
	variables := map[string]interface{}{
		"owner":  githubv4.String(owner),
		"name":   githubv4.String(repo),
		"first":  githubv4.Int(100),
		"cursor": (*githubv4.String)(nil),
	}

	for {
		err := client.Query(ctx, &query, variables)
		if err != nil {
			return nil, fmt.Errorf("GraphQL query failed: %w", err)
		}

		for _, contributor := range query.Repository.Collaborators.Nodes {
			contributors = append(contributors, ContributorInfo{
				Login:     string(contributor.Login),
				Name:      string(contributor.Name),
				Email:     string(contributor.Email),
				AvatarURL: string(contributor.AvatarURL.String()),
				URL:       string(contributor.URL.String()),
				Bio:       string(contributor.Bio),
				Company:   string(contributor.Company),
				Location:  string(contributor.Location),
			})
		}

		if !query.Repository.Collaborators.PageInfo.HasNextPage {
			break
		}
		variables["cursor"] = githubv4.NewString(query.Repository.Collaborators.PageInfo.EndCursor)
	}

	return contributors, nil
}

// SearchRepositoriesGraphQL searches for GitHub repositories using GraphQL
// This is more efficient than the REST API for searching repositories
func SearchRepositoriesGraphQL(ctx context.Context, client *githubv4.Client, query string, maxResults int) ([]RepositoryInfo, error) {
	var q struct {
		Search struct {
			RepositoryCount githubv4.Int
			Edges           []struct {
				Node struct {
					Repository struct {
						ID               githubv4.ID
						Name             githubv4.String
						NameWithOwner    githubv4.String
						Description      githubv4.String
						URL              githubv4.URI
						CreatedAt        githubv4.DateTime
						UpdatedAt        githubv4.DateTime
						PushedAt         githubv4.DateTime
						IsPrivate        githubv4.Boolean
						IsFork           githubv4.Boolean
						IsArchived       githubv4.Boolean
						ForkCount        githubv4.Int
						StargazerCount   githubv4.Int
						DefaultBranchRef struct {
							Name githubv4.String
						}
						Owner struct {
							Login githubv4.String
							URL   githubv4.URI
						}
						PrimaryLanguage struct {
							Name githubv4.String
						}
					} `graphql:"... on Repository"`
				}
			}
			PageInfo struct {
				HasNextPage bool
				EndCursor   githubv4.String
			}
		} `graphql:"search(query: $query, type: REPOSITORY, first: $first, after: $cursor)"`
	}

	var repos []RepositoryInfo
	variables := map[string]interface{}{
		"query":  githubv4.String(query),
		"first":  githubv4.Int(100),
		"cursor": (*githubv4.String)(nil),
	}

	for {
		err := client.Query(ctx, &q, variables)
		if err != nil {
			return nil, fmt.Errorf("GraphQL query failed: %w", err)
		}

		for _, edge := range q.Search.Edges {
			repo := edge.Node.Repository
			repos = append(repos, RepositoryInfo{
				ID:             fmt.Sprintf("%v", repo.ID),
				Name:           string(repo.Name),
				FullName:       string(repo.NameWithOwner),
				Description:    string(repo.Description),
				URL:            string(repo.URL.String()),
				CreatedAt:      repo.CreatedAt.Time,
				UpdatedAt:      repo.UpdatedAt.Time,
				PushedAt:       repo.PushedAt.Time,
				IsPrivate:      bool(repo.IsPrivate),
				IsFork:         bool(repo.IsFork),
				IsArchived:     bool(repo.IsArchived),
				DefaultBranch:  string(repo.DefaultBranchRef.Name),
				ForkCount:      int(repo.ForkCount),
				StargazerCount: int(repo.StargazerCount),
				Owner: OwnerInfo{
					Login: string(repo.Owner.Login),
					URL:   string(repo.Owner.URL.String()),
				},
				PrimaryLanguage: string(repo.PrimaryLanguage.Name),
			})

			if maxResults > 0 && len(repos) >= maxResults {
				return repos, nil
			}
		}

		if !q.Search.PageInfo.HasNextPage {
			break
		}
		variables["cursor"] = githubv4.NewString(q.Search.PageInfo.EndCursor)
	}

	return repos, nil
}

// GetRepoCommitsGraphQL retrieves commits for a GitHub repository using GraphQL
// Returns a slice of CommitInfo structures containing commit details
func GetRepoCommitsGraphQL(ctx context.Context, client *githubv4.Client, owner, repo string, maxCommits int) ([]CommitInfo, error) {
	var commits []CommitInfo
	var query struct {
		Repository struct {
			DefaultBranchRef struct {
				Name   githubv4.String
				Target struct {
					Commit struct {
						History struct {
							PageInfo struct {
								HasNextPage bool
								EndCursor   githubv4.String
							}
							Nodes []struct {
								Oid           githubv4.String
								CommittedDate githubv4.DateTime
								Message       githubv4.String
								Author        struct {
									Name  githubv4.String
									Email githubv4.String
								}
								Committer struct {
									Name  githubv4.String
									Email githubv4.String
								}
							}
						} `graphql:"history(first: $commitsPerPage)"`
					} `graphql:"... on Commit"`
				}
			}
		} `graphql:"repository(owner: $owner, name: $name)"`
	}

	variables := map[string]interface{}{
		"owner":          githubv4.String(owner),
		"name":           githubv4.String(repo),
		"commitsPerPage": githubv4.Int(100), // Fetch 100 commits per page
	}

	// First query to get initial commits
	err := client.Query(ctx, &query, variables)
	if err != nil {
		return nil, fmt.Errorf("GraphQL query failed: %w", err)
	}

	// Process first page of commits
	for _, node := range query.Repository.DefaultBranchRef.Target.Commit.History.Nodes {
		commits = append(commits, CommitInfo{
			SHA:            string(node.Oid),
			Message:        string(node.Message),
			CommittedDate:  node.CommittedDate.Time,
			AuthorName:     string(node.Author.Name),
			AuthorEmail:    string(node.Author.Email),
			CommitterName:  string(node.Committer.Name),
			CommitterEmail: string(node.Committer.Email),
		})

		// Check if we've reached the maximum number of commits
		if maxCommits > 0 && len(commits) >= maxCommits {
			return commits[:maxCommits], nil
		}
	}

	// Continue fetching pages until we have enough commits or there are no more pages
	hasNextPage := query.Repository.DefaultBranchRef.Target.Commit.History.PageInfo.HasNextPage
	endCursor := query.Repository.DefaultBranchRef.Target.Commit.History.PageInfo.EndCursor

	for hasNextPage && (maxCommits <= 0 || len(commits) < maxCommits) {
		var queryWithCursor struct {
			Repository struct {
				DefaultBranchRef struct {
					Target struct {
						Commit struct {
							History struct {
								PageInfo struct {
									HasNextPage bool
									EndCursor   githubv4.String
								}
								Nodes []struct {
									Oid           githubv4.String
									CommittedDate githubv4.DateTime
									Message       githubv4.String
									Author        struct {
										Name  githubv4.String
										Email githubv4.String
									}
									Committer struct {
										Name  githubv4.String
										Email githubv4.String
									}
								}
							} `graphql:"history(first: $commitsPerPage, after: $cursor)"`
						} `graphql:"... on Commit"`
					}
				}
			} `graphql:"repository(owner: $owner, name: $name)"`
		}

		variables["cursor"] = endCursor

		err := client.Query(ctx, &queryWithCursor, variables)
		if err != nil {
			// Return what we have so far with the error
			return commits, fmt.Errorf("GraphQL pagination query failed: %w", err)
		}

		// Process this page of commits
		for _, node := range queryWithCursor.Repository.DefaultBranchRef.Target.Commit.History.Nodes {
			commits = append(commits, CommitInfo{
				SHA:            string(node.Oid),
				Message:        string(node.Message),
				CommittedDate:  node.CommittedDate.Time,
				AuthorName:     string(node.Author.Name),
				AuthorEmail:    string(node.Author.Email),
				CommitterName:  string(node.Committer.Name),
				CommitterEmail: string(node.Committer.Email),
			})

			// Check if we've reached the maximum number of commits
			if maxCommits > 0 && len(commits) >= maxCommits {
				return commits[:maxCommits], nil
			}
		}

		// Update pagination info for next iteration
		hasNextPage = queryWithCursor.Repository.DefaultBranchRef.Target.Commit.History.PageInfo.HasNextPage
		endCursor = queryWithCursor.Repository.DefaultBranchRef.Target.Commit.History.PageInfo.EndCursor
	}

	return commits, nil
}

// GetLatestCommitGraphQL retrieves the latest commit for a GitHub repository using GraphQL
// Returns a Commit struct containing commit details and any error encountered
func GetLatestCommitGraphQL(ctx context.Context, client *githubv4.Client, owner string, repo string) (*Commit, error) {
	var query struct {
		Repository struct {
			DefaultBranchRef struct {
				Target struct {
					Commit struct {
						OID           githubv4.GitObjectID
						CommittedDate githubv4.DateTime
						Committer     struct {
							Name  githubv4.String
							Email githubv4.String
						}
					} `graphql:"... on Commit"`
				}
			}
		} `graphql:"repository(owner: $owner, name: $name)"`
	}

	variables := map[string]interface{}{
		"owner": githubv4.String(owner),
		"name":  githubv4.String(repo),
	}

	err := client.Query(ctx, &query, variables)
	if err != nil {
		return nil, fmt.Errorf("GraphQL query failed: %w", err)
	}

	commit := query.Repository.DefaultBranchRef.Target.Commit

	latestCommit := &Commit{
		SHA:   string(commit.OID),
		User:  string(commit.Committer.Name),
		Email: string(commit.Committer.Email),
		Date:  commit.CommittedDate.Time,
	}

	return latestCommit, nil
}
