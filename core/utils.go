package core

import (
	"bufio"
	"fmt"
	"io"
	"os"
	"os/exec"
	"os/user"
	"path"
	"path/filepath"
	"runtime"
	"strings"
	"time"

	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	"github.com/schollz/progressbar/v3"
)

const (
	TempDirName string = "octopi"
)

func TempDirPath() string {
	return path.Join(os.TempDir(), TempDirName)
}

func MakeTempDir() (string, error) {
	parentTempDir := TempDirPath()
	if err := os.MkdirAll(parentTempDir, 0o775); err != nil {
		return "", err
	}
	tempDirPath, err := os.MkdirTemp(parentTempDir, "")
	if err != nil {
		return "", err
	}
	return tempDirPath, nil
}

func CleanOnError(err *error, path string) {
	if *err != nil {
		os.RemoveAll(path)
	}
}

// EnsureCleanup ensures that temporary directories are cleaned up
func EnsureCleanup(paths ...string) {
	for _, path := range paths {
		if path == "" {
			continue
		}
		if _, err := os.Stat(path); err == nil {
			if err := os.RemoveAll(path); err != nil {
				log.Error().Msgf("Failed to clean up temporary directory %s: %v", path, err)
			} else {
				log.Debug().Msgf("Successfully cleaned up temporary directory: %s", path)
			}
		}
	}
}

// GetExecutableDir returns the directory where the current executable is located
func GetExecutableDir() (string, error) {
	execPath, err := os.Executable()
	if err != nil {
		return "", err
	}
	return filepath.Dir(execPath), nil
}

// CommandExists checks if a command exists in PATH or in the same directory as Octopi
func CommandExists(cmd string) bool {
	// First check if it exists in PATH
	if _, err := exec.LookPath(cmd); err == nil {
		return true
	}

	// If not in PATH, check in the same directory as Octopi
	execDir, err := GetExecutableDir()
	if err != nil {
		return false
	}

	binPath := filepath.Join(execDir, cmd)
	if runtime.GOOS == "windows" {
		binPath += ".exe"
	}

	if _, err := os.Stat(binPath); err == nil {
		return true
	}

	return false
}

// GetCommandPath returns the full path to a command, checking both PATH and Octopi's directory
func GetCommandPath(cmd string) string {
	// First check if it exists in PATH
	if path, err := exec.LookPath(cmd); err == nil {
		return path
	}

	// If not in PATH, check in the same directory as Octopi
	execDir, err := GetExecutableDir()
	if err != nil {
		return cmd // Fall back to just the command name
	}

	binPath := filepath.Join(execDir, cmd)
	if runtime.GOOS == "windows" {
		binPath += ".exe"
	}

	if _, err := os.Stat(binPath); err == nil {
		return binPath
	}

	return cmd // Fall back to just the command name
}

// ExpandPath expands tilde (~) in file paths to the user's home directory
func ExpandPath(path string) (string, error) {
	if !strings.HasPrefix(path, "~") {
		return path, nil
	}

	if path == "~" {
		// Just the home directory
		usr, err := user.Current()
		if err != nil {
			return "", err
		}
		return usr.HomeDir, nil
	}

	if strings.HasPrefix(path, "~/") {
		// Path relative to home directory
		usr, err := user.Current()
		if err != nil {
			return "", err
		}
		return filepath.Join(usr.HomeDir, path[2:]), nil
	}

	// Path like ~username/... - not supported for security reasons
	return path, nil
}

// FileExistsAndNotEmpty checks if a file exists and is not empty
// It supports tilde expansion for paths starting with ~
func FileExistsAndNotEmpty(path string) bool {
	expandedPath, err := ExpandPath(path)
	if err != nil {
		return false
	}
	s, err := os.Stat(expandedPath)
	return (err == nil) && s.Size() > 0
}

// GetSliceOfLines reads a reader and returns a slice of lines, skipping comments and empty lines
func GetSliceOfLines(r io.Reader) []string {
	var lines []string
	reader := bufio.NewScanner(r)
	if err := reader.Err(); err != nil {
		fmt.Fprintln(os.Stderr, "Error:", err)
	}
	reader.Split(bufio.ScanLines)
	for reader.Scan() {
		if strings.HasPrefix(reader.Text(), "#") {
			continue
		}
		if reader.Text() != "" {
			lines = append(lines, reader.Text())
		}
	}
	return lines
}

// IsUserIgnored checks if a user is in the ignore list
func IsUserIgnored(cfg *Config, user string) bool {
	m := make(map[string]struct{})
	for _, v := range cfg.IgnoreUsers {
		if _, exists := m[v]; !exists {
			m[v] = struct{}{}
		}
	}
	if _, ok := m[user]; ok {
		return true
	}
	return false
}

// IsRepoIgnored checks if a repository is in the ignore list
func IsRepoIgnored(cfg *Config, user, repo string) bool {
	m := make(map[string]struct{})
	for _, v := range cfg.IgnoreRepos {
		if _, exists := m[v]; !exists {
			m[v] = struct{}{}
		}
	}
	gRepo := fmt.Sprintf("%s/%s", user, repo)
	if _, ok := m[gRepo]; ok {
		return true
	}
	return false
}

// NormalizeGitHubURL standardizes GitHub repository URLs to a consistent format
// It handles various input formats like:
// - https://github.com/owner/repo
// - https://github.com/owner/repo.git
// - **************:owner/repo.git
// - owner/repo
// And returns a consistent https URL without .git suffix
func NormalizeGitHubURL(repoURL string) (string, error) {
	// Handle simple owner/repo format
	if !strings.Contains(repoURL, "://") && !strings.Contains(repoURL, "@") {
		parts := strings.Split(repoURL, "/")
		if len(parts) == 2 {
			return fmt.Sprintf("https://github.com/%s/%s", parts[0], parts[1]), nil
		}
	}

	// Handle git@ SSH format
	if strings.HasPrefix(repoURL, "**************:") {
		path := strings.TrimPrefix(repoURL, "**************:")
		path = strings.TrimSuffix(path, ".git")
		return fmt.Sprintf("https://github.com/%s", path), nil
	}

	// Handle https:// format
	if strings.HasPrefix(repoURL, "https://github.com/") {
		url := strings.TrimSuffix(repoURL, ".git")
		return url, nil
	}

	// Handle http:// format (convert to https)
	if strings.HasPrefix(repoURL, "http://github.com/") {
		url := strings.Replace(repoURL, "http://", "https://", 1)
		url = strings.TrimSuffix(url, ".git")
		return url, nil
	}

	return "", fmt.Errorf("unsupported GitHub repository URL format: %s", repoURL)
}

// ParseGitHubRepo extracts owner and repository name from a GitHub URL
// Returns owner, repo name, and error if any
func ParseGithubRepo(repoURL string) (string, string, error) {
	// First normalize the URL
	normalizedURL, err := NormalizeGitHubURL(repoURL)
	if err != nil {
		return "", "", err
	}

	// Extract owner and repo from normalized URL
	parts := strings.TrimPrefix(normalizedURL, "https://github.com/")
	segments := strings.Split(parts, "/")

	if len(segments) < 2 {
		return "", "", fmt.Errorf("invalid GitHub repository URL format: %s", repoURL)
	}

	owner := segments[0]
	repo := segments[1]

	return owner, repo, nil
}

// readLinesFromFile reads lines from a file and returns them as a slice
func ReadLinesFromFile(filename string) ([]string, error) {
	if filename == "" {
		return nil, nil
	}

	file, err := os.Open(filename)
	if err != nil {
		return nil, fmt.Errorf("failed to open file %s: %w", filename, err)
	}
	defer file.Close()

	var lines []string
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line != "" && !strings.HasPrefix(line, "#") { // Skip empty lines and comments
			lines = append(lines, line)
		}
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("error reading file %s: %w", filename, err)
	}

	return lines, nil
}

// SetupLogging configures zerolog for human-readable console output
func SetupLogging(debug bool, noColor bool) {
	// Set global log level based on debug flag
	if debug {
		zerolog.SetGlobalLevel(zerolog.DebugLevel)
	} else {
		zerolog.SetGlobalLevel(zerolog.InfoLevel)
	}

	// Configure zerolog with custom formatting
	zerolog.TimeFieldFormat = time.RFC3339 // Keep RFC3339 for structured logging

	// Create a custom console writer with prettier formatting
	consoleWriter := zerolog.ConsoleWriter{
		Out:        os.Stderr,
		NoColor:    noColor,
		TimeFormat: "15:04:05", // Short time format for console (HH:MM:SS)
		FormatLevel: func(i any) string {
			level := strings.ToUpper(fmt.Sprintf("%s", i))
			if noColor {
				switch level {
				case "DEBUG":
					return "[DEBUG]"
				case "INFO":
					return "[INFO]"
				case "WARN":
					return "[WARN]"
				case "ERROR":
					return "[ERROR]"
				case "FATAL":
					return "[FATAL]"
				default:
					return fmt.Sprintf("[%s]", level)
				}
			}
			switch level {
			case "DEBUG":
				return "\033[36m[DBG]\033[0m" // Cyan
			case "INFO":
				return "\033[32m[INF]\033[0m" // Green
			case "WARN":
				return "\033[33m[WRN]\033[0m" // Yellow
			case "ERROR":
				return "\033[31m[ERR]\033[0m" // Red
			case "FATAL":
				return "\033[35m[FTL]\033[0m" // Magenta
			default:
				return fmt.Sprintf("[%s]", level)
			}
		},
		FormatMessage: func(i any) string {
			return fmt.Sprintf("%s", i)
		},
		FormatFieldName: func(i any) string {
			if noColor {
				return fmt.Sprintf("%s=", i)
			}
			return fmt.Sprintf("\033[38;5;246m%s=\033[0m", i) // Gray field names
		},
		FormatFieldValue: func(i any) string {
			return fmt.Sprintf("%s", i)
		},
	}

	// Set the global logger to use our console writer
	log.Logger = log.Output(consoleWriter)
}

// Unique returns a slice with duplicate elements removed while preserving order
// Uses generics to work with any comparable type (strings, ints, etc.)
func Unique[T comparable](slice []T) []T {
	result := make([]T, 0, len(slice))
	seen := make(map[T]bool, len(slice))

	for _, element := range slice {
		if !seen[element] {
			result = append(result, element)
			seen[element] = true
		}
	}

	return result
}

// progressAwareLog handles logging while a progress bar is active
func progressAwareLog(bar *progressbar.ProgressBar, useProgressBar bool, level zerolog.Level, msg string) {
	// Check if this log level would actually be shown
	if level < zerolog.GlobalLevel() {
		return // Don't do anything if log won't be shown
	}

	if useProgressBar && bar != nil {
		// Store current state before clearing
		currentState := bar.State()
		bar.Clear()

		switch level {
		case zerolog.DebugLevel:
			log.Debug().Msg(msg)
		case zerolog.InfoLevel:
			log.Info().Msg(msg)
		case zerolog.WarnLevel:
			log.Warn().Msg(msg)
		case zerolog.ErrorLevel:
			log.Error().Msg(msg)
		}

		// Restore progress bar at current position
		_ = bar.Set(int(currentState.CurrentNum))
	} else {
		// Normal logging when no progress bar
		switch level {
		case zerolog.DebugLevel:
			log.Debug().Msg(msg)
		case zerolog.InfoLevel:
			log.Info().Msg(msg)
		case zerolog.WarnLevel:
			log.Warn().Msg(msg)
		case zerolog.ErrorLevel:
			log.Error().Msg(msg)
		}
	}
}

// progressAwareLogf handles formatted logging while a progress bar is active
func progressAwareLogf(bar *progressbar.ProgressBar, useProgressBar bool, level zerolog.Level, format string, args ...any) {
	// Check if this log level would actually be shown
	if level < zerolog.GlobalLevel() {
		return // Don't do anything if log won't be shown
	}

	if useProgressBar && bar != nil {
		// Store current state before clearing
		currentState := bar.State()
		bar.Clear()

		switch level {
		case zerolog.DebugLevel:
			log.Debug().Msgf(format, args...)
		case zerolog.InfoLevel:
			log.Info().Msgf(format, args...)
		case zerolog.WarnLevel:
			log.Warn().Msgf(format, args...)
		case zerolog.ErrorLevel:
			log.Error().Msgf(format, args...)
		}

		// Restore progress bar at current position
		_ = bar.Set(int(currentState.CurrentNum))
	} else {
		// Normal logging when no progress bar
		switch level {
		case zerolog.DebugLevel:
			log.Debug().Msgf(format, args...)
		case zerolog.InfoLevel:
			log.Info().Msgf(format, args...)
		case zerolog.WarnLevel:
			log.Warn().Msgf(format, args...)
		case zerolog.ErrorLevel:
			log.Error().Msgf(format, args...)
		}
	}
}
