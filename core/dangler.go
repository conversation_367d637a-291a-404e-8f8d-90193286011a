package core

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	"github.com/schollz/progressbar/v3"
)

const forkCommitFactor = 0.001 // 0.1% additional commits per fork

// Dangler configuration for object discovery
type Dangler struct {
	Enabled            bool    // Whether to enable brute force discovery
	MaxCharLength      int     // Maximum SHA character length to test (4-6)
	CollisionThreshold float64 // Threshold for estimated SHA collisions
	ChunkSize          int     // Number of commits to test per GraphQL query
	MaxQueries         int     // Maximum number of GraphQL queries to make
	GithubToken        string  // GitHub token for GraphQL access
	CacheDir           string  // Directory to cache results and restore state
	TestCommits        string  // Test specific commits (comma-separated) for debugging
}

// DanglerResult contains discovered commits
type DanglerResult struct {
	ValidCommits   []string `json:"valid_commits"`
	InvalidCommits []string `json:"invalid_commits"`
	TotalTested    int      `json:"total_tested"`
	QueriesMade    int      `json:"queries_made"`
	Duration       string   `json:"duration"`
}

// GraphQLCommitData represents commit data from GraphQL response
type GraphQLCommitData struct {
	OID string `json:"oid"`
}

// GraphQLResponse represents the GraphQL API response
type GraphQLResponse struct {
	Data struct {
		Repository map[string]GraphQLCommitData `json:"repository"`
	} `json:"data"`
	Errors []struct {
		Message string `json:"message"`
	} `json:"errors"`
	Message string `json:"message"`
}

// RunDanglerDiscovery executes brute force commit discovery
func (s *Scan) RunDanglerDiscovery(ctx context.Context, clonePath string) ([]string, error) {
	start := time.Now()

	// Create cache directory
	if s.Dangler.CacheDir == "" {
		defaultCacheDir, err := getDefaultCacheDir()
		if err != nil {
			return nil, err
		}
		s.Dangler.CacheDir = defaultCacheDir
	}
	cacheDir := filepath.Join(s.Dangler.CacheDir, s.Octopi.Owner, s.Octopi.RepoName)
	if err := os.MkdirAll(cacheDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create cache directory %s: %w", cacheDir, err)
	}

	// Check if test commits are specified for debugging
	if s.Dangler.TestCommits != "" {
		log.Info().Msg("Testing specific commits for debugging")
		testCommits := strings.Split(s.Dangler.TestCommits, ",")
		var cleanCommits []string
		for _, commit := range testCommits {
			cleanCommit := strings.TrimSpace(commit)
			if cleanCommit != "" {
				cleanCommits = append(cleanCommits, cleanCommit)
			}
		}

		if len(cleanCommits) > 0 {
			log.Info().Msgf("Testing %d specific commits: %v", len(cleanCommits), cleanCommits)
			validCommits, invalidCommits, err := s.batchTestCommits(ctx, cacheDir, cleanCommits)
			if err != nil {
				return nil, fmt.Errorf("failed to test specific commits: %w", err)
			}

			log.Info().Msgf("Test results: %d valid, %d invalid commits", len(validCommits), len(invalidCommits))
			for _, commit := range validCommits {
				log.Info().Msgf("✅ Valid commit: %s", commit)
			}
			for _, commit := range invalidCommits {
				log.Info().Msgf("❌ Invalid commit: %s", commit)
			}

			// For test mode, return all valid commits (for debugging purposes)
			return validCommits, nil
		}
	}

	// count total valid hashes
	reachableHashes, err := getReachableHashes(clonePath)
	if err != nil {
		return nil, fmt.Errorf("failed to enumerate existing commit object hashes: %w", err)
	}
	log.Debug().Msgf("Found %d reachable commits in Git repository", len(reachableHashes))

	// Check if discovery was already completed
	completionFile := filepath.Join(cacheDir, ".discovery_complete")
	if _, err := os.Stat(completionFile); err == nil {
		log.Info().Msgf("Dangler discovery already completed, loading cached results")
		validCommits, err := s.loadCachedResults(cacheDir)
		if err != nil {
			log.Warn().Msgf("Failed to load cached valid commits: %v", err)
			// Remove completion marker and continue with discovery
			os.Remove(completionFile)
		} else {
			log.Info().Msgf("Loaded %d cached valid commits", len(validCommits))
			return validCommits, nil
		}
	}

	// Get repository metadata for collision calculation
	repoInfo, err := s.Github.GetRepository(ctx, s.Octopi.Owner, s.Octopi.RepoName)
	if err != nil {
		return nil, fmt.Errorf("failed to get repository info: %w", err)
	}

	// Calculate optimal SHA length based on repository size
	shaLength, estimatedUsedKeys, collisions := s.calculateOptimalSHALength(len(reachableHashes), repoInfo.GetForksCount())
	log.Info().Msgf("Estimated used keys: %d", estimatedUsedKeys)
	log.Info().Msgf("Estimated collisions: %d", collisions)
	log.Info().Msgf("Using Short SHA-1 length: %d characters", shaLength)

	// Load cached results (both valid and invalid)
	cachedValidCommits, _ := s.loadCachedResults(cacheDir)
	cachedInvalidCommits, _ := s.loadCachedInvalidCommits(cacheDir)

	if len(cachedValidCommits) > 0 {
		log.Info().Msgf("Found %d cached valid commits", len(cachedValidCommits))
	}
	if len(cachedInvalidCommits) > 0 {
		log.Info().Msgf("Found %d cached invalid commits", len(cachedInvalidCommits))
	}

	// Generate all possible SHA combinations
	possibleSHAs := s.generateSHACombinations(shaLength)
	log.Info().Msgf("Generated %d possible SHA combinations", len(possibleSHAs))

	// Filter out already processed commits
	filteredSHAs := s.filterProcessedCommits(possibleSHAs, cachedValidCommits, cachedInvalidCommits, reachableHashes, shaLength)
	log.Info().Msgf("Testing %d SHA combinations after filtering cached results", len(filteredSHAs))

	// If no new SHAs to test, return cached results
	if len(filteredSHAs) == 0 {
		log.Info().Msgf("No new SHA combinations to test, returning cached results")
		return cachedValidCommits, nil
	}

	// Test commits in batches using GraphQL (batchTestCommits will merge with cached results)
	validCommits, invalidCommits, err := s.batchTestCommits(ctx, cacheDir, filteredSHAs)
	if err != nil {
		return nil, fmt.Errorf("failed to test commits: %w", err)
	}

	// Check if the process was interrupted
	interrupted := ctx.Err() != nil

	// Cache both valid and invalid results
	log.Debug().Msgf("Caching results to directory: %s", cacheDir)
	log.Debug().Msgf("Valid commits to cache: %d, Invalid commits to cache: %d", len(validCommits), len(invalidCommits))
	if err := s.cacheCommitsToDisk(validCommits, invalidCommits, cacheDir); err != nil {
		log.Error().Msgf("Failed to cache results to %s: %v", cacheDir, err)
	} else {
		log.Info().Msgf("Successfully cached %d valid and %d invalid commits to %s", len(validCommits), len(invalidCommits), cacheDir)
	}

	duration := time.Since(start)
	if interrupted {
		log.Warn().Msgf("Dangler discovery interrupted after %v, found %d valid commits (partial results)", duration, len(validCommits))
	} else {
		log.Info().Msgf("Dangler discovery completed in %v, found %d valid commits", duration, len(validCommits))
	}

	return validCommits, nil
}

// calculateOptimalSHALength calculates the optimal SHA length to avoid collisions
func (s *Scan) calculateOptimalSHALength(commitCount, forksCount int) (int, int, int) {
	commits := float64(commitCount)
	forks := float64(forksCount)

	if forks > 0 {
		commits += commits * forkCommitFactor * forks // 0.1% per fork
	}

	// Calculate minimum SHA length to avoid collisions
	for length := 4; length <= s.Dangler.MaxCharLength; length++ {
		keySpace := float64(uint64(1) << uint(length*4)) // 16^length
		collisions := (commits * (commits - 1)) / (2 * keySpace)

		if collisions <= s.Dangler.CollisionThreshold {
			return length, int(keySpace), int(collisions)
		}
	}

	// Fallback to maximum if no safe length found
	keySpace := float64(uint64(1) << uint(s.Dangler.MaxCharLength*4))
	collisions := (commits * (commits - 1)) / (2 * keySpace)
	return s.Dangler.MaxCharLength, int(keySpace), int(collisions)
}

// generateSHACombinations generates all possible SHA combinations for given length
func (s *Scan) generateSHACombinations(length int) []string {
	hexDigits := "0123456789abcdef"
	var combinations []string

	var generate func(prefix string, remaining int)
	generate = func(prefix string, remaining int) {
		if remaining == 0 {
			combinations = append(combinations, prefix)
			return
		}
		for _, digit := range hexDigits {
			generate(prefix+string(digit), remaining-1)
		}
	}

	generate("", length)
	return combinations
}

// filterProcessedCommits removes SHA combinations that have already been processed
func (s *Scan) filterProcessedCommits(possibleSHAs, cachedValid, cachedInvalid, reachable []string, shaLength int) []string {
	// Create sets for faster lookup
	validSet := make(map[string]struct{})
	invalidSet := make(map[string]struct{})
	reachableSet := make(map[string]struct{})

	// Add cached valid commits (truncated to shaLength)
	for _, commit := range cachedValid {
		shortSHA := commit
		if len(commit) > shaLength {
			shortSHA = commit[:shaLength]
		}
		validSet[shortSHA] = struct{}{}
	}

	// Add GIT repository reachable commits (truncated to shaLength)
	for _, commit := range reachable {
		shortSHA := commit
		if len(commit) > shaLength {
			shortSHA = commit[:shaLength]
		}
		reachableSet[shortSHA] = struct{}{}
	}

	// Add cached invalid commits
	for _, commit := range cachedInvalid {
		invalidSet[commit] = struct{}{}
	}

	// Filter out already processed SHAs
	var filtered []string
	for _, sha := range possibleSHAs {
		if _, exists := validSet[sha]; !exists {
			if _, exists := invalidSet[sha]; !exists {
				if _, exists := reachableSet[sha]; !exists {
					filtered = append(filtered, sha)
				}
			}
		}
	}

	return filtered
}

// batchTestCommits tests commits in batches with improved error handling and progress tracking
func (s *Scan) batchTestCommits(ctx context.Context, cacheDir string, shas []string) ([]string, []string, error) {
	// Load existing cached results to merge with new results
	cachedValidCommits, _ := s.loadCachedResults(cacheDir)
	cachedInvalidCommits, _ := s.loadCachedInvalidCommits(cacheDir)

	var validCommits []string
	var invalidCommits []string

	// Start with existing cached results
	validCommits = append(validCommits, cachedValidCommits...)
	invalidCommits = append(invalidCommits, cachedInvalidCommits...)

	// Adaptive chunk size with backoff
	chunkSize := s.Dangler.ChunkSize
	needsProcessing := make([]string, len(shas))
	copy(needsProcessing, shas)

	startingSize := len(needsProcessing)
	queriesMade := 0
	completed := false   // Track if discovery was completed or interrupted
	interrupted := false // Track if context was canceled

	log.Info().Msgf("Starting batch processing of %d SHA combinations", startingSize)

	// Initialize progress bar - only show if not in debug mode and there's work to do
	var bar *progressbar.ProgressBar
	var useProgressBar bool = startingSize > 0 && zerolog.GlobalLevel() != zerolog.DebugLevel

	if useProgressBar {
		bar = progressbar.NewOptions(startingSize,
			progressbar.OptionSetDescription("Processing commits"),
			progressbar.OptionSetWriter(os.Stderr),
			progressbar.OptionEnableColorCodes(true),
			progressbar.OptionShowCount(),
			progressbar.OptionShowIts(),
			progressbar.OptionSetItsString("commits"),
			progressbar.OptionThrottle(500*time.Millisecond),
			progressbar.OptionSetWidth(30),
			progressbar.OptionOnCompletion(func() {
				fmt.Fprint(os.Stderr, "\n")
			}),
			progressbar.OptionSetTheme(progressbar.Theme{
				Saucer:        "[green]=[reset]",
				SaucerHead:    "[green]>[reset]",
				SaucerPadding: " ",
				BarStart:      "[",
				BarEnd:        "]",
			}),
		)
		// Initialize the progress bar display
		_ = bar.Set(0)
	}

	for len(needsProcessing) > 0 && queriesMade < s.Dangler.MaxQueries {
		// Check if context is canceled before starting each batch
		if ctx.Err() != nil {
			progressAwareLogf(bar, useProgressBar, zerolog.WarnLevel, "Dangler discovery interrupted, stopping at batch %d", queriesMade)
			interrupted = true
			break
		}

		// Adjust chunk size if needed
		if len(needsProcessing) < chunkSize {
			chunkSize = len(needsProcessing)
		}

		batch := needsProcessing[:chunkSize]
		needsProcessing = needsProcessing[chunkSize:]

		valid, invalid, err := s.checkHashes(ctx, batch)
		if err != nil {
			// Check if this is a context cancellation (expected during shutdown)
			if ctx.Err() != nil {
				progressAwareLog(bar, useProgressBar, zerolog.WarnLevel, "Dangler discovery interrupted (context canceled)")
				interrupted = true
				break
			}

			// Handle rate limiting
			if strings.Contains(err.Error(), "rate limit") || strings.Contains(err.Error(), "secondary rate limit") {
				progressAwareLog(bar, useProgressBar, zerolog.WarnLevel, "Hit GitHub rate limit, sleeping for 60 seconds...")
				time.Sleep(60 * time.Second)
				// Re-add the failed batch back to processing queue
				needsProcessing = append(needsProcessing, batch...)
				continue
			}

			progressAwareLogf(bar, useProgressBar, zerolog.WarnLevel, "Error testing batch %d: %v", queriesMade+1, err)
			// Reduce chunk size on error
			if chunkSize > 10 {
				chunkSize = chunkSize / 2
				progressAwareLogf(bar, useProgressBar, zerolog.DebugLevel, "Reduced chunk size to %d due to error", chunkSize)
			}
			// Re-add the failed batch back to processing queue
			needsProcessing = append(needsProcessing, batch...)
			queriesMade++
			continue
		}

		// Success - process results
		validCommits = append(validCommits, valid...)
		invalidCommits = append(invalidCommits, invalid...)
		queriesMade++

		// Update progress bar
		if bar != nil {
			processed := startingSize - len(needsProcessing)
			_ = bar.Set(processed)
		}

		// Incremental cache save after each successful batch (only new results)
		if len(valid) > 0 || len(invalid) > 0 {
			if err := s.cacheCommitsToDisk(valid, invalid, cacheDir); err != nil {
				return nil, nil, fmt.Errorf("failed to incrementally cache batch results: %w", err)
			}
			// Only log cache operations in debug mode to avoid progress bar conflicts
			progressAwareLogf(bar, useProgressBar, zerolog.DebugLevel, "Incrementally cached %d valid, %d invalid commits from batch", len(valid), len(invalid))
		}

		// Increase chunk size on success (up to original config value)
		if chunkSize < s.Dangler.ChunkSize {
			chunkSize = min(chunkSize*2, s.Dangler.ChunkSize)
			progressAwareLogf(bar, useProgressBar, zerolog.DebugLevel, "Increased chunk size to %d after success", chunkSize)
		}

		// Rate limiting - small delay between requests
		time.Sleep(100 * time.Millisecond)
	}

	// Final check for context cancellation right before completion
	if ctx.Err() != nil {
		interrupted = true
	}

	// Mark as completed ONLY if we weren't interrupted
	completed = !interrupted

	// Finish progress bar (only if it was created)
	if useProgressBar && bar != nil {
		if completed {
			_ = bar.Finish()
		} else {
			// Clear progress bar if interrupted
			bar.Clear()
			fmt.Fprint(os.Stderr, "\n") // Add newline after clearing
		}
	}

	// Return unique results (remove duplicates from merging cached + new results)
	validCommits = Unique(validCommits)
	invalidCommits = Unique(invalidCommits)

	if completed {
		// Mark discovery as completed in cache
		completionFile := filepath.Join(cacheDir, ".discovery_complete")
		if err := os.WriteFile(completionFile, []byte("completed"), 0644); err != nil {
			log.Warn().Msgf("Failed to write completion marker: %v", err)
		}
		log.Info().Msgf("Batch processing completed: %d valid, %d invalid commits found", len(validCommits), len(invalidCommits))
	} else {
		log.Info().Msgf("Batch processing interrupted: %d valid, %d invalid commits found (partial results)", len(validCommits), len(invalidCommits))
	}
	return validCommits, invalidCommits, nil
}

// checkHashes tests a batch of commits and returns both valid and invalid commits
func (s *Scan) checkHashes(ctx context.Context, shas []string) ([]string, []string, error) {
	// Build GraphQL query for batch testing
	var testCases strings.Builder
	for _, sha := range shas {
		testCases.WriteString(fmt.Sprintf(`
			commit%s: object(expression: "%s") {
				... on Commit {
					oid
				}
			}
		`, sha, sha))
	}

	query := fmt.Sprintf(`
		query {
			repository(owner: "%s", name: "%s") {
				%s
			}
		}
	`, s.Octopi.Owner, s.Octopi.RepoName, testCases.String())

	// Execute GraphQL query
	response, err := s.executeGraphQLQuery(ctx, query)
	if err != nil {
		return nil, nil, err
	}

	// Parse results
	var validCommits []string
	var invalidCommits []string

	for commitKey, commitData := range response.Data.Repository {
		// Remove "commit" prefix to get original SHA
		sha := strings.TrimPrefix(commitKey, "commit")

		if commitData.OID == "" || commitData.OID == "{}" {
			invalidCommits = append(invalidCommits, sha)
		} else {
			validCommits = append(validCommits, commitData.OID)
		}
	}

	return validCommits, invalidCommits, nil
}

// executeGraphQLQuery executes a GraphQL query against GitHub API
func (s *Scan) executeGraphQLQuery(ctx context.Context, query string) (*GraphQLResponse, error) {
	requestBody, err := json.Marshal(map[string]string{"query": query})
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", "https://api.github.com/graphql", bytes.NewBuffer(requestBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Add headers (assuming we have access to GitHub token)
	req.Header.Set("Authorization", "Bearer "+s.Dangler.GithubToken)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 120 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	var response GraphQLResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	if len(response.Errors) > 0 {
		return nil, fmt.Errorf("GraphQL error: %s", response.Errors[0].Message)
	}

	return &response, nil
}

// Helper methods for caching
func (s *Scan) loadCachedResults(cacheDir string) ([]string, error) {
	validFile := filepath.Join(cacheDir, "valid_hidden.txt")
	if _, err := os.Stat(validFile); os.IsNotExist(err) {
		return []string{}, fmt.Errorf("no cache found")
	}

	data, err := os.ReadFile(validFile)
	if err != nil {
		return []string{}, fmt.Errorf("failed to read cache file: %w", err)
	}

	lines := strings.Split(string(data), "\n")
	var commits []string
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line != "" {
			commits = append(commits, line)
		}
	}

	return Unique(commits), nil
}

func (s *Scan) loadCachedInvalidCommits(cacheDir string) ([]string, error) {
	invalidFile := filepath.Join(cacheDir, "invalid.txt")
	if _, err := os.Stat(invalidFile); os.IsNotExist(err) {
		return []string{}, nil
	}

	data, err := os.ReadFile(invalidFile)
	if err != nil {
		return []string{}, fmt.Errorf("failed to read invalid cache file: %w", err)
	}

	lines := strings.Split(string(data), "\n")
	var commits []string
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line != "" {
			commits = append(commits, line)
		}
	}

	return Unique(commits), nil
}

func (s *Scan) cacheCommitsToDisk(validCommits, invalidCommits []string, cacheDir string) error {
	// Cache valid commits
	if len(validCommits) > 0 {
		validFile := filepath.Join(cacheDir, "valid_hidden.txt")
		file, err := os.OpenFile(validFile, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
		if err != nil {
			return fmt.Errorf("failed to open valid cache file %s: %w", validFile, err)
		}
		defer file.Close()

		for _, commit := range validCommits {
			if _, err := file.WriteString(commit + "\n"); err != nil {
				return fmt.Errorf("failed to write valid commit %s: %w", commit, err)
			}
		}
	}

	// Cache invalid commits
	if len(invalidCommits) > 0 {
		invalidFile := filepath.Join(cacheDir, "invalid.txt")
		file, err := os.OpenFile(invalidFile, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
		if err != nil {
			return fmt.Errorf("failed to open invalid cache file %s: %w", invalidFile, err)
		}
		defer file.Close()

		for _, commit := range invalidCommits {
			if _, err := file.WriteString(commit + "\n"); err != nil {
				return fmt.Errorf("failed to write invalid commit %s: %w", commit, err)
			}
		}
	}

	return nil
}

func getReachableHashes(path string) ([]string, error) {
	var hashes []string
	gitArgs := []string{
		"-C",
		path,
		"--work-tree",
		path,
		"cat-file",
		"--batch-check",
		"--batch-all-objects",
	}
	outputBytes, err := runGitCommand(gitArgs)
	if err != nil {
		return hashes, err
	}

	output := string(outputBytes)
	lines := strings.SplitSeq(output, "\n")
	for line := range lines {
		if len(line) > 0 {
			parts := strings.Fields(line)
			if len(parts) > 0 {
				hashes = append(hashes, parts[0])
			}
		}
	}
	return hashes, nil
}

// getDefaultCacheDir returns the path to the Dangler cache directory
func getDefaultCacheDir() (string, error) {
	configDir, err := GetDefaultConfigDir()
	if err != nil {
		return "", fmt.Errorf("failed to get the default config directory: %w", err)
	}
	reportsDir := filepath.Join(configDir, "dangler-cache")
	if err := os.MkdirAll(reportsDir, 0o775); err != nil {
		return "", fmt.Errorf("cannot create Dangler cache directory: %w", err)
	}
	return reportsDir, nil
}
