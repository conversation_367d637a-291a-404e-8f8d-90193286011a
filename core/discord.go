package core

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"

	"github.com/rs/zerolog/log"
)

// DiscordMessage represents the structure of a Discord webhook message
type DiscordMessage struct {
	Content   string         `json:"content,omitempty"`
	Username  string         `json:"username,omitempty"`
	AvatarURL string         `json:"avatar_url,omitempty"`
	Embeds    []DiscordEmbed `json:"embeds,omitempty"`
}

// DiscordEmbed represents a rich embed in a Discord message
type DiscordEmbed struct {
	Title       string              `json:"title,omitempty"`
	Description string              `json:"description,omitempty"`
	URL         string              `json:"url,omitempty"`
	Color       int                 `json:"color,omitempty"` // Decimal color value
	Fields      []DiscordEmbedField `json:"fields,omitempty"`
	Thumbnail   *DiscordThumbnail   `json:"thumbnail,omitempty"`
	Footer      *DiscordFooter      `json:"footer,omitempty"`
}

// DiscordEmbed<PERSON>ield represents a field in a Discord embed
type DiscordEmbed<PERSON>ield struct {
	Name   string `json:"name,omitempty"`
	Value  string `json:"value,omitempty"`
	Inline bool   `json:"inline,omitempty"`
}

// DiscordThumbnail represents a thumbnail in a Discord embed
type DiscordThumbnail struct {
	URL string `json:"url,omitempty"`
}

// DiscordFooter represents a footer in a Discord embed
type DiscordFooter struct {
	Text    string `json:"text,omitempty"`
	IconURL string `json:"icon_url,omitempty"`
}

// SendDiscord sends a notification message to a Discord channel via webhook
func SendDiscord(webhook string, message Notification) error {
	// Validate input
	if webhook == "" {
		return fmt.Errorf("empty Discord webhook URL")
	}

	// Redact secret for security if it's a scan notification
	redactedSecret := "*****"
	if message.Type == ScanNotification && len(message.Secret) > 7 {
		redactedSecret = message.Secret[:3] + "***" + message.Secret[len(message.Secret)-3:]
	}

	// Create fields based on notification type
	var fields []DiscordEmbedField
	var title string
	var description string
	var color int
	var ghLink string

	switch message.Type {
	case ScanNotification:
		// Format for scan notifications
		title = "🔐 Security Alert"
		description = "Octopi detected a potential secret"
		color = 16711680 // Red color (decimal for #FF0000)

		// GitHub code link for scans
		if message.File != "" && message.Commit != "" {
			ghLink = fmt.Sprintf("%s/blob/%s/%s#L%d",
				message.RepoURL, message.Commit, url.PathEscape(message.File), message.Line)
		} else {
			ghLink = message.RepoURL
		}

		fields = []DiscordEmbedField{
			{
				Name:   "Rule",
				Value:  fmt.Sprintf("`%s`", message.Rule),
				Inline: true,
			},
			{
				Name:   "Secret",
				Value:  fmt.Sprintf("`%s`", redactedSecret),
				Inline: true,
			},
			{
				Name:   "File",
				Value:  fmt.Sprintf("`%s`", message.File),
				Inline: true,
			},
			{
				Name:   "Author",
				Value:  message.Author,
				Inline: true,
			},
			{
				Name: "Commit",
				Value: fmt.Sprintf("[`%s`](%s/commit/%s)",
					message.Commit[:7], message.RepoURL, message.Commit),
				Inline: true,
			},
			{
				Name: "Repository",
				Value: fmt.Sprintf("[%s](%s)",
					message.RepoURL, message.RepoURL),
				Inline: true,
			},
			{
				Name:   "Timestamp",
				Value:  message.Timestamp,
				Inline: true,
			},
		}

		// Add dangler information if it's a dangling commit
		if message.DanglerCommit {
			fields = append(fields, DiscordEmbedField{
				Name:   "⛳ Dangling Commit",
				Value:  "This secret was found in a dangling commit",
				Inline: false,
			})
		}

	case WatchNotification:
		// Format for watch notifications
		title = "📡 Commit Watch"
		description = "Octopi detected a watched commit"
		color = 16753920 // Orange color (decimal for #FFA500)

		// GitHub commit link for watches
		ghLink = fmt.Sprintf("%s/commit/%s",
			message.RepoURL, message.Commit)

		fields = []DiscordEmbedField{
			{
				Name:   "Author",
				Value:  message.Author,
				Inline: true,
			},
			{
				Name: "Commit",
				Value: fmt.Sprintf("[`%s`](%s/commit/%s)",
					message.Commit[:7], message.RepoURL, message.Commit),
				Inline: true,
			},
			{
				Name: "Repository",
				Value: fmt.Sprintf("[%s](%s)",
					message.RepoURL, message.RepoURL),
				Inline: true,
			},
			{
				Name:   "Timestamp",
				Value:  message.Timestamp,
				Inline: true,
			},
			{
				Name:   "Watch Filter",
				Value:  fmt.Sprintf("`%s`", message.Query),
				Inline: false,
			},
		}

		// Add commit message if available
		if message.CommitMsg != "" {
			fields = append(fields, DiscordEmbedField{
				Name:   "Commit Message",
				Value:  fmt.Sprintf("```%s```", message.CommitMsg),
				Inline: false,
			})
		}
	}

	// Create Discord embed
	embed := DiscordEmbed{
		Title:       title,
		Description: description,
		URL:         ghLink,
		Color:       color,
		Fields:      fields,
		Footer: &DiscordFooter{
			Text:    fmt.Sprintf("Octopi v%s", Version),
			IconURL: "https://github.githubassets.com/images/modules/logos_page/GitHub-Mark.png",
		},
	}

	// Create Discord message
	discordMsg := DiscordMessage{
		Username:  "Octopi",
		AvatarURL: "https://github.githubassets.com/images/modules/logos_page/GitHub-Mark.png",
		Embeds:    []DiscordEmbed{embed},
	}

	// Convert message to JSON
	jsonData, err := json.Marshal(discordMsg)
	if err != nil {
		return fmt.Errorf("error marshaling Discord message: %w", err)
	}

	// Send HTTP request
	log.Debug().Msgf("Sending Discord notification for rule: %s", message.Rule)
	resp, err := http.Post(webhook, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("error sending Discord webhook: %w", err)
	}
	defer resp.Body.Close()

	// Check response status
	if resp.StatusCode < 200 || resp.StatusCode > 299 {
		return fmt.Errorf("Discord webhook returned non-2xx status code: %d", resp.StatusCode)
	}

	log.Debug().Msgf("Successfully sent Discord notification for rule: %s", message.Rule)
	return nil
}
