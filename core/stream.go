package core

import (
	"bufio"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/rs/zerolog/log"
)

// StreamOptions contains configuration for GitHub event streaming
type StreamOptions struct {
	AuthToken        string
	Rate             int
	SearchAllCommits bool
}

// CommitEvent represents a single commit event from GitHub
type CommitEvent struct {
	Timestamp   time.Time
	UserName    string
	RepoName    string
	SHA         string
	Message     string
	AuthorName  string
	AuthorEmail struct {
		User   string
		Domain string
	}
	ModifiedFiles []string // List of files modified in this commit
	AddedFiles    []string // List of files added in this commit
	RemovedFiles  []string // List of files removed in this commit
	IsSensitive   bool     // Whether this commit touches sensitive files
	RepoMetadata  struct { // Repository metadata for filtering
		IsForked   bool
		IsArchived bool
		SizeKB     int
		StarCount  int
		CreatedAt  time.Time
	}
}

// CommitFilter contains filtering options for commit events
type CommitFilter struct {
	AuthorName         string
	EmailDomain        string
	DomainsFile        string
	DomainsList        map[string]bool
	IgnorePrivate      bool
	SearchAllCommits   bool
	Enabled            bool
	SensitiveFiles     []string        // List of sensitive file patterns to monitor
	SensitiveFilesMap  map[string]bool // Map for faster lookup
	SensitivePathsFile string          // Path to file containing sensitive file patterns
	AlertOnSensitive   bool            // Whether to alert when sensitive files are modified
	MaxFileSizeMB      int             // Maximum file size to fetch (MB), 0 = no limit
	RepoFilters        RepoFilters     // Repository filtering options
}

// StreamStats tracks statistics for the GitHub event stream
type StreamStats struct {
	IncomingRate  uint32
	ProcessedRate uint32
	FilteredRate  uint32
	mu            sync.Mutex
}

// IncrementIncoming increments the incoming rate counter
func (s *StreamStats) IncrementIncoming() {
	atomic.AddUint32(&s.IncomingRate, 1)
}

// IncrementProcessed increments the processed rate counter
func (s *StreamStats) IncrementProcessed() {
	atomic.AddUint32(&s.ProcessedRate, 1)
}

// IncrementFiltered increments the filtered rate counter
func (s *StreamStats) IncrementFiltered() {
	atomic.AddUint32(&s.FilteredRate, 1)
}

// LoadDomainsList loads a list of domains from a file
func (f *CommitFilter) LoadDomainsList() error {
	f.DomainsList = make(map[string]bool)
	file, err := os.Open(f.DomainsFile)
	if err != nil {
		return err
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		f.DomainsList[scanner.Text()] = true
	}

	if err := scanner.Err(); err != nil {
		return err
	}

	log.Info().Msgf("Loaded %d domains from %s", len(f.DomainsList), f.DomainsFile)
	f.Enabled = true
	return nil
}

// LoadSensitiveFilesList loads a list of sensitive file patterns from a file
func (f *CommitFilter) LoadSensitiveFilesList() error {
	f.SensitiveFilesMap = make(map[string]bool)
	file, err := os.Open(f.SensitivePathsFile)
	if err != nil {
		return err
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		pattern := strings.TrimSpace(scanner.Text())
		if pattern != "" && !strings.HasPrefix(pattern, "#") {
			f.SensitiveFiles = append(f.SensitiveFiles, pattern)
			f.SensitiveFilesMap[pattern] = true
		}
	}

	if err := scanner.Err(); err != nil {
		return err
	}

	log.Info().Msgf("Loaded %d sensitive file patterns from %s", len(f.SensitiveFiles), f.SensitivePathsFile)
	f.Enabled = true
	return nil
}

// InitializeSensitiveFiles initializes the sensitive files list from command line arguments
func (f *CommitFilter) InitializeSensitiveFiles(patterns []string) {
	f.SensitiveFilesMap = make(map[string]bool)
	for _, pattern := range patterns {
		pattern = strings.TrimSpace(pattern)
		if pattern != "" {
			f.SensitiveFiles = append(f.SensitiveFiles, pattern)
			f.SensitiveFilesMap[pattern] = true
		}
	}

	if len(f.SensitiveFiles) > 0 {
		log.Info().Msgf("Monitoring %d sensitive file patterns", len(f.SensitiveFiles))
		f.Enabled = true
	}
}

// IsSensitiveFile checks if a file path matches any of the sensitive file patterns
func (f *CommitFilter) IsSensitiveFile(filePath string) bool {
	if len(f.SensitiveFiles) == 0 {
		return false
	}

	// Check exact matches first
	if f.SensitiveFilesMap[filePath] {
		return true
	}

	// Check pattern matches
	for _, pattern := range f.SensitiveFiles {
		// Support glob patterns
		if matched, _ := filepath.Match(pattern, filepath.Base(filePath)); matched {
			return true
		}

		// Support path contains matching
		if strings.Contains(filePath, pattern) {
			return true
		}

		// Support case-insensitive matching for common patterns
		if strings.Contains(strings.ToLower(filePath), strings.ToLower(pattern)) {
			return true
		}
	}

	return false
}

// CheckCommitForSensitiveFiles checks if a commit touches any sensitive files
func (f *CommitFilter) CheckCommitForSensitiveFiles(commit *CommitEvent) {
	if len(f.SensitiveFiles) == 0 {
		return
	}

	allFiles := append(commit.ModifiedFiles, commit.AddedFiles...)
	allFiles = append(allFiles, commit.RemovedFiles...)

	for _, file := range allFiles {
		if f.IsSensitiveFile(file) {
			commit.IsSensitive = true
			log.Warn().Msgf("Sensitive file detected in commit %s: %s", commit.SHA[:7], file)
			break
		}
	}
}

// CheckRepositoryFilters filters repositories based on configured criteria
func (f *CommitFilter) CheckRepositoryFilters(commit *CommitEvent) bool {
	// If no repo filters configured, allow all
	if f.RepoFilters.NewRepositories == 0 && f.RepoFilters.RepoSizeLimit == 0 && f.RepoFilters.StarCountMin == 0 {
		return true
	}

	// Check if repository is too new
	if f.RepoFilters.NewRepositories > 0 {
		daysSinceCreation := int(time.Since(commit.RepoMetadata.CreatedAt).Hours() / 24)
		if daysSinceCreation <= f.RepoFilters.NewRepositories {
			log.Debug().Msgf("Repository %s/%s is too new (%d days old)",
				commit.UserName, commit.RepoName, daysSinceCreation)
			return false
		}
	}

	// Check if repository is forked (and we don't want forked repos)
	if !f.RepoFilters.ForkedRepos && commit.RepoMetadata.IsForked {
		log.Debug().Msgf("Excluding forked repository %s/%s", commit.UserName, commit.RepoName)
		return false
	}

	// Check if repository is archived (and we don't want archived repos)
	if !f.RepoFilters.ArchivedRepos && commit.RepoMetadata.IsArchived {
		log.Debug().Msgf("Excluding archived repository %s/%s", commit.UserName, commit.RepoName)
		return false
	}

	// Check repository size limit (convert KB to MB)
	if f.RepoFilters.RepoSizeLimit > 0 {
		repoSizeMB := commit.RepoMetadata.SizeKB / 1024
		if repoSizeMB > f.RepoFilters.RepoSizeLimit {
			log.Debug().Msgf("Repository %s/%s is too large (%d MB > %d MB limit)",
				commit.UserName, commit.RepoName, repoSizeMB, f.RepoFilters.RepoSizeLimit)
			return false
		}
	}

	// Check minimum star count
	if f.RepoFilters.StarCountMin > 0 && commit.RepoMetadata.StarCount < f.RepoFilters.StarCountMin {
		log.Debug().Msgf("Repository %s/%s has too few stars (%d < %d minimum)",
			commit.UserName, commit.RepoName, commit.RepoMetadata.StarCount, f.RepoFilters.StarCountMin)
		return false
	}

	return true
}

// NewCommitFilterFromProfile creates a CommitFilter from a WatchProfile
func NewCommitFilterFromProfile(profile WatchProfile) *CommitFilter {
	// Set default max file size if not specified (50MB is a sensible default)
	maxFileSizeMB := profile.MaxFileSizeMB
	if maxFileSizeMB == 0 {
		maxFileSizeMB = 50 // Default to 50MB
	}

	filter := &CommitFilter{
		AuthorName:         strings.Join(profile.AuthorNames, ","),
		EmailDomain:        strings.Join(profile.EmailDomains, ","),
		DomainsFile:        profile.DomainsFile,
		IgnorePrivate:      profile.IgnorePrivate,
		SearchAllCommits:   profile.SearchAllCommits,
		SensitivePathsFile: profile.SensitivePathsFile,
		AlertOnSensitive:   profile.AlertOnSensitive,
		MaxFileSizeMB:      maxFileSizeMB,
		RepoFilters:        profile.RepoFilters,
		Enabled:            true,
	}

	// Initialize sensitive files if provided
	if len(profile.SensitiveFiles) > 0 {
		filter.InitializeSensitiveFiles(profile.SensitiveFiles)
	}

	return filter
}

// ShouldFetchFiles determines if file fetching should be enabled based on configuration
func (profile WatchProfile) ShouldFetchFiles() bool {
	// Auto-enable file fetching if sensitive files are configured
	return len(profile.SensitiveFiles) > 0 || profile.SensitivePathsFile != ""
}

// Match checks if a commit matches the filter criteria
func (f *CommitFilter) Match(commit CommitEvent) bool {
	if !f.Enabled {
		log.Debug().Msg("Filter is disabled, allowing all commits")
		return true
	}

	log.Debug().Msgf("Filtering commit from %s <%s@%s>",
		commit.AuthorName, commit.AuthorEmail.User, commit.AuthorEmail.Domain)

	// Check repository filters first
	if !f.CheckRepositoryFilters(&commit) {
		log.Debug().Msgf("Commit %s filtered out due to repository criteria", commit.SHA[:7])
		return false
	}

	// Check for sensitive files first (this is a positive filter - we want to alert on these)
	if f.AlertOnSensitive && commit.IsSensitive {
		log.Warn().Msgf("ALERT: Sensitive file modification detected in commit %s by %s",
			commit.SHA[:7], commit.AuthorName)
		return true // Always allow sensitive file commits through for alerting
	}

	// Check if we should ignore private GitHub emails
	if f.IgnorePrivate {
		// Check for various GitHub private email patterns
		if strings.HasSuffix(commit.AuthorEmail.Domain, "users.noreply.github.com") ||
			strings.Contains(commit.AuthorEmail.Domain, "noreply.github.com") {
			log.Debug().Msgf("Ignoring private GitHub email: %s@%s",
				commit.AuthorEmail.User, commit.AuthorEmail.Domain)
			return false
		}
	}

	// Check email domain filter
	if f.EmailDomain != "" {
		domains := strings.Split(f.EmailDomain, ",")
		matched := false
		for _, domain := range domains {
			if strings.EqualFold(commit.AuthorEmail.Domain, domain) {
				matched = true
				break
			}
		}
		if !matched {
			return false
		}
	}

	// Check domains list filter
	if len(f.DomainsList) > 0 {
		if !f.DomainsList[commit.AuthorEmail.Domain] {
			return false
		}
	}

	// Check author name filter
	if f.AuthorName != "" {
		names := strings.Split(f.AuthorName, ",")
		matched := false
		for _, name := range names {
			if strings.Contains(strings.ToLower(commit.AuthorName), strings.ToLower(name)) {
				matched = true
				break
			}
		}
		if !matched {
			return false
		}
	}

	log.Debug().Msgf("Commit passed all filters: %s <%s@%s>",
		commit.AuthorName, commit.AuthorEmail.User, commit.AuthorEmail.Domain)
	return true
}
