package core

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math"
	"net/http"
	"net/http/cookiejar"
	"net/url"
	"os"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"math/rand/v2"

	"github.com/google/go-github/v72/github"
	"github.com/pquerna/otp/totp"
	"github.com/rs/zerolog/log"
	"github.com/shurcooL/githubv4"
	"golang.org/x/oauth2"
	"golang.org/x/time/rate"
)

const (
	// Default values for GitHub API interaction
	GithubInitialPage           = 1   // Initial page for pagination
	GithubDefaultPagination     = 100 // Default number of items per page
	GithubPaginationRateSeconds = 3   // Seconds between pagination requests
	GithubPollingRateSeconds    = 20  // Seconds between polling cycles
)

// RateLimiter manages rate limiting state and backoff logic
type RateLimiter struct {
	mu         sync.RWMutex // Mutex for concurrent access
	resumeTime time.Time    // Time to resume after rate limit
	attempts   int          // Number of attempts for backoff
}

// RateLimitedTransport provides better rate limit handling
type RateLimitedTransport struct {
	Transport     http.RoundTripper // Underlying transport to use
	Limiter       *rate.Limiter     // Rate limiter for API requests
	RetryAttempts int               // Current retry attempts
	MaxRetries    int               // Maximum retry attempts
	RetryDelay    time.Duration     // Initial retry delay
}

// RoundTrip implements the http.RoundTripper interface with enhanced rate limiting
func (t *RateLimitedTransport) RoundTrip(req *http.Request) (*http.Response, error) {
	// Wait for rate limit token
	ctx := req.Context()
	if err := t.Limiter.Wait(ctx); err != nil {
		return nil, fmt.Errorf("rate limit wait error: %w", err)
	}

	// Use the underlying transport to perform the request
	resp, err := t.Transport.RoundTrip(req)

	// Handle rate limit errors with exponential backoff
	if resp != nil && (resp.StatusCode == 429 || resp.StatusCode == 403) {
		if t.RetryAttempts < t.MaxRetries {
			t.RetryAttempts++

			// Get retry-after header or use exponential backoff
			retryAfter := t.RetryDelay
			if retryAfterStr := resp.Header.Get("Retry-After"); retryAfterStr != "" {
				if seconds, err := strconv.Atoi(retryAfterStr); err == nil {
					retryAfter = time.Duration(seconds) * time.Second
				}
			} else {
				// Exponential backoff with jitter
				backoffSeconds := math.Pow(2, float64(t.RetryAttempts))
				jitter := rand.Float64() * 0.5 // Add up to 50% jitter
				retryAfter = time.Duration(backoffSeconds*(1+jitter)) * time.Second
			}

			select {
			case <-ctx.Done():
				return nil, ctx.Err()
			case <-time.After(retryAfter):
				// Create a new request with the same details
				newReq := req.Clone(ctx)
				return t.RoundTrip(newReq)
			}
		}
	}

	// Reset retry counter on success
	if resp != nil && resp.StatusCode < 400 {
		t.RetryAttempts = 0
	}

	return resp, err
}

// CreateGithubClient creates an authenticated GitHub client with rate limiting
func CreateGithubClient(ctx context.Context, token string) (*github.Client, *githubv4.Client, error) {
	if token == "" {
		token = os.Getenv("GITHUB_TOKEN")
		if token == "" {
			return nil, nil, fmt.Errorf("GitHub API token not set. Please use --token/-t flag or set GITHUB_TOKEN environment variable.")
		}
	}

	ts := oauth2.StaticTokenSource(
		&oauth2.Token{AccessToken: token},
	)
	tc := oauth2.NewClient(ctx, ts)

	// Create a rate-limited transport with better defaults
	tc.Transport = &RateLimitedTransport{
		Transport:  tc.Transport,
		Limiter:    rate.NewLimiter(rate.Limit(5), 10), // 5 requests per second, burst of 10
		MaxRetries: 3,
		RetryDelay: time.Second,
	}

	return github.NewClient(tc), githubv4.NewClient(tc), nil
}

// SearchGithubCode searches GitHub Code Search API for a query
// Returns a slice of code search results and any error encountered
func SearchCode(ctx context.Context, ghc *github.Client, query string) ([]*github.CodeResult, error) {
	var codeResult []*github.CodeResult

	ghSearchOpts := &github.SearchOptions{
		ListOptions: github.ListOptions{
			Page:    GithubInitialPage,
			PerPage: GithubDefaultPagination,
		},
	}

	for {
		// Check if context is cancelled before making the request
		if err := ctx.Err(); err != nil {
			return codeResult, err
		}

		codeSearchResult, res, err := ghc.Search.Code(ctx, query, ghSearchOpts)

		if handled := handleRateLimit(err); handled {
			continue
		}
		if err != nil {
			return nil, fmt.Errorf("Github code search failed: %w", err)
		}

		if len(codeSearchResult.CodeResults) > 0 {
			codeResult = append(codeResult, codeSearchResult.CodeResults...)
		}

		// Log progress for large result sets
		if len(codeResult) > 0 && len(codeResult)%1000 == 0 {
			log.Debug().Msgf("Retrieved %d code results so far for query: %s", len(codeResult), query)
		}

		if res == nil || res.NextPage == 0 {
			break
		}
		ghSearchOpts.ListOptions.Page = res.NextPage
	}

	log.Debug().Msgf("Found %d code result(s) for query: %s", len(codeResult), query)
	return codeResult, nil
}

// SearchCommits searches GitHub Commits Search API for a query
// Returns a slice of commit results and any error encountered
func SearchCommits(ctx context.Context, ghc *github.Client, query string) ([]*github.CommitResult, error) {
	var commitResult []*github.CommitResult

	ghSearchOpts := &github.SearchOptions{
		ListOptions: github.ListOptions{
			Page:    GithubInitialPage,
			PerPage: GithubDefaultPagination,
		},
	}

	for {
		commitSearchResult, res, err := ghc.Search.Commits(ctx, query, ghSearchOpts)

		if handled := handleRateLimit(err); handled {
			continue
		}
		if err != nil {
			return nil, fmt.Errorf("Github commits search failed: %w", err)
		}
		if len(commitSearchResult.Commits) > 0 {
			commitResult = append(commitResult, commitSearchResult.Commits...)
		}
		if res == nil || res.NextPage == 0 {
			break
		}
		ghSearchOpts.ListOptions.Page = res.NextPage
	}
	return commitResult, nil
}

// SearchGithubUsers searches Github Users Search API for a query
// and retrieves all public repositories of the found user(s).
func SearchUsers(ctx context.Context, ghc *github.Client, query string) ([]*github.Repository, error) {

	var userResult []*github.User
	var userRepoResult []*github.Repository

	ghSearchOpts := &github.SearchOptions{
		ListOptions: github.ListOptions{
			Page:    GithubInitialPage,
			PerPage: GithubDefaultPagination,
		},
	}

	for {
		userSearchResult, res, err := ghc.Search.Users(ctx, query, ghSearchOpts)

		if handled := handleRateLimit(err); handled {
			continue
		}
		if err != nil {
			return nil, fmt.Errorf("Github users search failed: %w", err)
		}
		for _, user := range userSearchResult.Users {
			userResult = append(userResult, user)
		}
		if res == nil || res.NextPage == 0 {
			break
		}
		ghSearchOpts.ListOptions.Page = res.NextPage
	}

	log.Debug().Msgf("Found %d user(s) for user search query: \"%s\"", len(userResult), query)

	for _, u := range userResult {
		repos, err := GetUserRepos(ctx, ghc, u.GetLogin())
		if err != nil {
			return nil, fmt.Errorf("%w", err)
		}
		userRepoResult = append(userRepoResult, repos...)
		log.Debug().Msgf("Found %d repo(s) for user: %s", len(repos), u.GetLogin())
	}

	return userRepoResult, nil
}

// GetUserRepos retrieves all repositories for a GitHub user
// Returns a slice of repositories and any error encountered
func GetUserRepos(ctx context.Context, ghc *github.Client, user string) ([]*github.Repository, error) {
	var userRepoResult []*github.Repository

	ghRepoOpts := &github.RepositoryListByUserOptions{
		ListOptions: github.ListOptions{
			Page:    GithubInitialPage,
			PerPage: GithubDefaultPagination,
		},
	}

	for {
		repos, res, err := ghc.Repositories.ListByUser(ctx, user, ghRepoOpts)
		if handled := handleRateLimit(err); handled {
			continue
		}

		if err != nil {
			return nil, fmt.Errorf("could not list user repos: %w", err)
		}

		for _, repo := range repos {
			userRepoResult = append(userRepoResult, repo)
		}

		if res == nil || res.NextPage == 0 {
			break
		}
		ghRepoOpts.ListOptions.Page = res.NextPage
	}

	return userRepoResult, nil
}

// GetOrgMembers retrieves all public members of a GitHub organization
// Returns a slice of users and any error encountered
func GetOrgMembers(ctx context.Context, ghc *github.Client, org string) ([]*github.User, error) {
	var orgMembers []*github.User
	ghListMemberOpts := &github.ListMembersOptions{
		PublicOnly: true,
		ListOptions: github.ListOptions{
			Page:    GithubInitialPage,
			PerPage: GithubDefaultPagination,
		},
	}

	for {
		members, res, err := ghc.Organizations.ListMembers(ctx, org, ghListMemberOpts)
		if handled := handleRateLimit(err); handled {
			continue
		}
		if err != nil {
			return nil, fmt.Errorf("could not list org repos: %w", err)
		}
		if len(members) > 0 {
			orgMembers = append(orgMembers, members...)
		}
		if res == nil || res.NextPage == 0 {
			break
		}
		ghListMemberOpts.ListOptions.Page = res.NextPage
	}
	return orgMembers, nil

}

// GetReposByOrg retrieves all repositories for a GitHub organization
// Returns a slice of repositories and any error encountered
func GetReposByOrg(ctx context.Context, ghc *github.Client, org string) ([]*github.Repository, error) {
	var orgReposResults []*github.Repository

	ghListByOrgOpts := &github.RepositoryListByOrgOptions{
		Type: "public",
		ListOptions: github.ListOptions{
			Page:    GithubInitialPage,
			PerPage: GithubDefaultPagination,
		},
	}

	for {
		repos, res, err := ghc.Repositories.ListByOrg(ctx, org, ghListByOrgOpts)
		if handled := handleRateLimit(err); handled {
			continue
		}
		if err != nil {
			return nil, fmt.Errorf("could not list org repos: %w", err)
		}
		if len(repos) > 0 {
			orgReposResults = append(orgReposResults, repos...)
		}
		if res == nil || res.NextPage == 0 {
			break
		}
		ghListByOrgOpts.ListOptions.Page = res.NextPage
	}
	return orgReposResults, nil
}

// GetContributors retrieves all contributors for a GitHub repository
// Returns a slice of contributors and any error encountered
func GetContributors(ctx context.Context, client *github.Client, owner, repo string) ([]*github.Contributor, error) {
	var allContributors []*github.Contributor
	opts := &github.ListContributorsOptions{
		ListOptions: github.ListOptions{PerPage: GithubDefaultPagination},
	}

	for {
		contributors, resp, err := client.Repositories.ListContributors(ctx, owner, repo, opts)

		if handled := handleRateLimit(err); handled {
			continue
		}
		if err != nil {
			return nil, fmt.Errorf("could not list repo contributors: %w", err)
		}

		allContributors = append(allContributors, contributors...)
		if resp.NextPage == 0 {
			break
		}
		opts.Page = resp.NextPage
	}

	return allContributors, nil
}

// IsOrgMember checks if a user is a member of a GitHub organization
func IsOrgMember(ctx context.Context, ghc *github.Client, org, user string) bool {
	var (
		err      error
		isMember bool
	)

	for {
		isMember, _, err = ghc.Organizations.IsMember(ctx, org, user)
		if handled := handleRateLimit(err); handled {
			continue
		}
		if err != nil {
			return false
		}
		break
	}
	return isMember
}

// GetUserCompany retrieves the company of a GitHub user
func GetUserCompany(ctx context.Context, ghc *github.Client, user string) string {
	var (
		err    error
		ghUser *github.User
	)

	for {
		ghUser, _, err = ghc.Users.Get(ctx, user)
		if handled := handleRateLimit(err); handled {
			continue
		}
		if err != nil {
			return "Independent"
		}
		break
	}

	company := ghUser.Company
	if company == nil {
		return "independent"
	}

	return *company
}

// GetRepoSize retrieves the size of a GitHub repository in kilobytes
func (g *Github) GetRepoSize(ctx context.Context, owner, repo string) (int, error) {
	ghRepo, err := g.GetRepository(ctx, owner, repo)
	if err != nil {
		return 0, err
	}
	return ghRepo.GetSize(), nil
}

// GetRepository retrieves details for a specific GitHub repository
// Returns a repository object and any error encountered
func (g *Github) GetRepository(ctx context.Context, owner, repo string) (*github.Repository, error) {
	var ghRepo *github.Repository
	var err error

	cacheKey := fmt.Sprintf("repo:%s/%s", owner, repo)
	if cached, found := g.Cache.Get("github_repo", cacheKey); found {
		if cachedRepo, ok := cached.(*github.Repository); ok && cachedRepo != nil {
			return cachedRepo, nil
		}
	}

	for {
		ghRepo, _, err = g.Client.Repositories.Get(ctx, owner, repo)
		if handled := handleRateLimit(err); handled {
			continue
		}

		if err != nil {
			return nil, fmt.Errorf("Unable to find GitHub repository: %w", err)
		}
		break
	}

	g.Cache.Set("github_repo", cacheKey, ghRepo, g.CacheTTL)

	return ghRepo, err
}

// GetLatestCommit retrieves the latest commit for a GitHub repository
// Returns a Commit struct containing commit details and any error encountered
func GetLatestCommit(ctx context.Context, ghc *github.Client, owner string, repo string) (*Commit, error) {
	var latestCommit *Commit
	var latestCommitSHA string
	var latestCommitUser string
	var latestCommitEmail string
	var latestCommitDate time.Time

	ghCommitOpts := &github.CommitsListOptions{
		ListOptions: github.ListOptions{
			Page:    0,
			PerPage: 1,
		},
	}

	for {
		commits, res, err := ghc.Repositories.ListCommits(ctx, owner, repo, ghCommitOpts)

		if handled := handleRateLimit(err); handled {
			continue
		}
		if err != nil || res == nil || res.StatusCode == 409 || res.StatusCode >= 500 {
			return nil, fmt.Errorf("failed to get Github repo latest commit: %w", err)
		}
		if len(commits) != 0 {
			latestCommitSHA = commits[0].GetSHA()
			latestCommitUser = commits[0].Commit.Committer.GetName()
			latestCommitEmail = commits[0].Commit.Committer.GetEmail()
			latestCommitDate = commits[0].Commit.Committer.GetDate().Time.UTC()
		} else {
			return nil, fmt.Errorf("no commits found for repo: %s/%s", owner, repo)
		}
		break
	}

	latestCommit = &Commit{
		SHA:   latestCommitSHA,
		User:  latestCommitUser,
		Email: latestCommitEmail,
		Date:  latestCommitDate,
	}
	return latestCommit, nil
}

// SearchPayload represents the JSON payload returned by the GitHub search API
type SearchPayload struct {
	Payload struct {
		Results []struct {
			Path         string `json:"path"`          // Path to the file containing the match
			RepoId       int    `json:"repo_id"`       // Repository ID
			RepoNwo      string `json:"repo_nwo"`      // Repository name with owner
			CommitSha    string `json:"commit_sha"`    // Commit SHA
			LanguageName string `json:"language_name"` // Language name
		} `json:"results"`
		PageCount   int `json:"page_count"`   // Total number of pages
		ResultCount int `json:"result_count"` // Total number of results
	} `json:"payload"`
}

// SearchCodeWithUI searches GitHub Code Search API for a query using the UI
func (g *Github) SearchCodeWithUI(ctx context.Context, query string) ([]*github.CodeResult, error) {
	var codeResult []*github.CodeResult
	resultMap := make(map[int]bool)
	base := "https://github.com/search"
	page, pages, delay, resultCount := 1, 1, 5, 0
	var resultPayload SearchPayload

	for page <= pages {
		surl := ConstructSearchURL(base, query, strconv.Itoa(page))
		response, err := g.HTTPClient.Get(surl)
		if err != nil {
			if response != nil {
				if response.StatusCode == 403 {
					response.Body.Close()
					delay += 5
					log.Warn().Msgf("Exceeded GitHub rate limit, retrying after: %ds...", delay)
					time.Sleep(time.Duration(delay) * time.Second)
				} else if response.StatusCode == 503 {
					log.Error().Msgf("Github is down for maintenance or overloaded")
					break
				}
			} else {
				log.Error().Msgf("Failed to get Github search results: %s", err.Error())
				break
			}
			continue
		}
		if delay > 10 {
			delay--
		}
		responseData, err := io.ReadAll(response.Body)
		responseStr := string(responseData)
		if err != nil {
			log.Error().Msgf("Failed to read Github response body: %s", err.Error())
			break
		}

		resultRegex := regexp.MustCompile("(?s)react-app\\.embeddedData\">(.*?)<\\/script>")
		match := resultRegex.FindStringSubmatch(responseStr)

		if len(match) != 2 {
			log.Error().Msgf("Failed to read Github search results")
			break
		}
		json.Unmarshal([]byte(match[1]), &resultPayload)

		pages = resultPayload.Payload.PageCount
		resultCount = resultPayload.Payload.ResultCount
		if resultCount == 0 {
			log.Error().Msgf("Search query did not match any code: '%s'", query)
			break
		}

		log.Debug().Msgf("Searching page %d of %d page(s) of results for '%s'...", page, pages, query)

		for _, result := range resultPayload.Payload.Results {
			if resultMap[(result.RepoId)] == true {
				continue
			}
			resultMap[(result.RepoId)] = true

			owner, repo, err := ParseGithubRepo(result.RepoNwo)
			if err != nil {
				return nil, err
			}

			ghRepo, err := g.GetRepository(ctx, owner, repo)
			if err != nil {
				return nil, err
			}
			codeResult = append(codeResult, &github.CodeResult{
				Name:       &result.RepoNwo,
				Repository: ghRepo,
			})

			// var repoSearchResult []RepoSearchResult
			// repoSearchResult = append(repoSearchResult, RepoSearchResult{
			// 	RepoID:   strconv.Itoa(result.RepoId),
			// 	RepoName: result.RepoNwo,
			// 	File:     result.Path,
			// 	Raw:      result.RepoNwo + "/" + result.CommitSha + "/" + result.Path,
			// 	Source:   "repo",
			// 	Query:    query,
			// 	URL:      "https://github.com/" + result.RepoNwo + "/blob/" + result.CommitSha + "/" + result.Path,
			// })
		}
		page++
	}
	return codeResult, nil
}

// GetRepoCommits retrieves all commits for a GitHub repository
// Returns a slice of repository commits and any error encountered
func GetRepoCommits(ctx context.Context, client *github.Client, owner, repo string) ([]*github.RepositoryCommit, error) {
	var allCommits []*github.RepositoryCommit
	opts := &github.CommitsListOptions{
		ListOptions: github.ListOptions{PerPage: GithubDefaultPagination},
	}

	for {
		commits, resp, err := client.Repositories.ListCommits(ctx, owner, repo, opts)

		if handled := handleRateLimit(err); handled {
			continue
		}
		if err != nil {
			return nil, fmt.Errorf("could not list repo commits: %w", err)
		}

		allCommits = append(allCommits, commits...)
		if resp.NextPage == 0 {
			break
		}
		opts.Page = resp.NextPage
	}

	return allCommits, nil
}

// HeaderTransport allows setting custom headers on HTTP requests
type HeaderTransport struct {
	Transport http.RoundTripper // Underlying transport to use
	headers   map[string]string // Headers to set on each request
}

// RoundTrip implements the http.RoundTripper interface
func (t *HeaderTransport) RoundTrip(req *http.Request) (*http.Response, error) {
	for key, value := range t.headers {
		req.Header.Set(key, value)
	}
	if t.Transport == nil {
		return http.DefaultTransport.RoundTrip(req)
	}
	return t.Transport.RoundTrip(req)
}

// Set adds or updates a header in the transport
func (t *HeaderTransport) Set(key, value string) {
	if t.headers == nil {
		t.headers = make(map[string]string)
	}
	t.headers[key] = value
}

// GitHubCredentials represents the credentials needed to login to GitHub
type GitHubCredentials struct {
	Username string
	Password string
	OTP      string
}

// LoginToGitHub logs into GitHub with the given credentials and returns an HTTP client.
func LoginToGitHub(credentials GitHubCredentials) (httpClient *http.Client, err error) {
	// Create a client with cookie support
	jar, err := cookiejar.New(nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create cookie jar: %w", err)
	}

	client := &http.Client{
		Jar:     jar,
		Timeout: 30 * time.Second, // Add a reasonable timeout
	}

	// Set up headers to mimic a browser
	rt := &HeaderTransport{Transport: client.Transport}
	rt.Set("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36")
	rt.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8")
	rt.Set("Accept-Language", "en-US,en;q=0.5")
	client.Transport = rt

	// Get CSRF token from login page
	csrf, err := GrabCSRFToken("https://github.com/login", client)
	if err != nil {
		return nil, fmt.Errorf("failed to get CSRF token: %w", err)
	}

	// Attempt login
	resp, err := client.PostForm("https://github.com/session", url.Values{
		"authenticity_token": {csrf},
		"login":              {credentials.Username},
		"password":           {credentials.Password},
	})
	if err != nil {
		return nil, fmt.Errorf("login request failed: %w", err)
	}
	defer resp.Body.Close()

	// Read response body
	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}
	dataStr := string(data)

	// Check for login errors
	if strings.Contains(dataStr, "Incorrect username or password") {
		return nil, fmt.Errorf("incorrect username or password")
	}

	// Handle 2FA if needed
	if strings.Contains(dataStr, "app_otp") {
		csrf, err = GrabCSRFTokenBody(dataStr)
		if err != nil {
			return nil, fmt.Errorf("failed to get CSRF token for 2FA: %w", err)
		}

		otp := HandleOTPCode(credentials)
		if otp == "" {
			return nil, fmt.Errorf("2FA required but no OTP provided")
		}

		// Handle different 2FA paths
		var twoFAURL string
		if strings.Contains(resp.Request.URL.String(), "verified-device") {
			twoFAURL = "https://github.com/sessions/verified-device"
		} else {
			twoFAURL = "https://github.com/sessions/two-factor"
		}

		resp, err = client.PostForm(twoFAURL, url.Values{
			"authenticity_token": {csrf},
			"otp":                {otp},
		})
		if err != nil {
			return nil, fmt.Errorf("2FA request failed: %w", err)
		}
		defer resp.Body.Close()

		// Check if 2FA was successful
		if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusFound {
			return nil, fmt.Errorf("2FA authentication failed with status: %d", resp.StatusCode)
		}
	}

	return client, nil
}

// HandleOTPCode returns a user's OTP code for authenticating with Github
func HandleOTPCode(credentials GitHubCredentials) string {
	if credentials.OTP == "" {
		return ""
	}

	// Generate a TOTP code based on TOTP seed
	otp, err := totp.GenerateCode(credentials.OTP, time.Now())
	if err != nil {
		log.Warn().Msgf("Failed to generate TOTP code: %s", err)
		return ""
	}
	return otp
}

// GrabCSRFToken retrieves a CSRF token from a GitHub page
// Returns the token string and any error encountered
func GrabCSRFToken(csrfURL string, client *http.Client) (token string, err error) {
	resp, err := client.Get(csrfURL)
	if err != nil {
		return "", fmt.Errorf("error getting CSRF token page: %w", err)
	}
	defer resp.Body.Close()

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("error reading response body: %w", err)
	}

	return GrabCSRFTokenBody(string(data))
}

var (
	// Regular expression to extract CSRF token from GitHub pages
	csrfTokenRegex = regexp.MustCompile(`<input type="hidden" name="authenticity_token" value="([^"]+)"`)
)

// GrabCSRFTokenBody extracts a CSRF token from a GitHub page body using regex
// Returns the token string and any error encountered
func GrabCSRFTokenBody(pageBody string) (token string, err error) {
	match := csrfTokenRegex.FindStringSubmatch(pageBody)
	if len(match) == 2 {
		return match[1], nil
	}
	return "", fmt.Errorf("could not find CSRF token in page body")
}

// ConstructSearchURL serializes its parameters into a search URL
func ConstructSearchURL(base, query, page string) string {
	// Pre-allocate a buffer with a reasonable size estimate
	var sb strings.Builder
	sb.Grow(len(base) + len(query) + 20)

	sb.WriteString(base)
	sb.WriteString("?q=")
	sb.WriteString(url.QueryEscape(query))
	sb.WriteString("&type=code&p=")
	sb.WriteString(page)

	return sb.String()
}

// FetchCommitFiles fetches the list of files modified in a commit with size checking
func FetchCommitFiles(ctx context.Context, client *github.Client, owner, repo, sha string) ([]string, []string, []string, error) {
	return FetchCommitFilesWithSizeLimit(ctx, client, owner, repo, sha, 50) // Default 50MB limit
}

// FetchCommitFilesWithSizeLimit fetches the list of files modified in a commit with a specified size limit
func FetchCommitFilesWithSizeLimit(ctx context.Context, client *github.Client, owner, repo, sha string, maxSizeMB int) ([]string, []string, []string, error) {
	commit, _, err := client.Repositories.GetCommit(ctx, owner, repo, sha, nil)
	if err != nil {
		return nil, nil, nil, err
	}

	var modified, added, removed []string
	var skippedFiles []string
	maxSizeBytes := int64(maxSizeMB * 1024 * 1024) // Convert MB to bytes

	for _, file := range commit.Files {
		filename := file.GetFilename()
		status := file.GetStatus()

		// Check if we have file size information and if it exceeds the limit
		// Note: GitHub API doesn't always provide file size in commit data
		// We'll check the additions + deletions as a rough proxy for file size impact
		if file.Additions != nil && file.Deletions != nil {
			totalChanges := int64(*file.Additions + *file.Deletions)
			// Rough estimate: if changes are very large, the file is likely large
			// This is not perfect but helps avoid fetching commits with massive file changes
			if totalChanges > maxSizeBytes/100 { // Rough heuristic: 1% of max size in changes
				skippedFiles = append(skippedFiles, filename)
				log.Debug().Msgf("Skipping large file %s in commit %s (changes: %d)", filename, sha[:7], totalChanges)
				continue
			}
		}

		switch status {
		case "modified":
			modified = append(modified, filename)
		case "added":
			added = append(added, filename)
		case "removed":
			removed = append(removed, filename)
		case "renamed":
			// Treat renamed files as both removed (old name) and added (new name)
			if file.GetPreviousFilename() != "" {
				removed = append(removed, file.GetPreviousFilename())
			}
			added = append(added, filename)
		}
	}

	// Log if we skipped any files
	if len(skippedFiles) > 0 {
		log.Warn().Msgf("Skipped %d large files in commit %s: %s",
			len(skippedFiles), sha[:7], strings.Join(skippedFiles, ", "))
	}

	return modified, added, removed, nil
}

// StreamGitHubEventsWithFileDetails connects to GitHub's event firehose and streams commit events with optional file details
func StreamGitHubEventsWithFileDetails(ctx context.Context, client *github.Client, allCommits bool, fetchFiles bool, results chan<- []CommitEvent, stats *StreamStats) error {
	return StreamGitHubEventsWithFileLimits(ctx, client, allCommits, fetchFiles, 50, results, stats) // Default 50MB
}

// StreamGitHubEventsWithFileLimits connects to GitHub's event firehose and streams commit events with file details and size limits
func StreamGitHubEventsWithFileLimits(ctx context.Context, client *github.Client, allCommits bool, fetchFiles bool, maxFileSizeMB int, results chan<- []CommitEvent, stats *StreamStats) error {
	return streamGitHubEventsInternal(ctx, client, allCommits, fetchFiles, maxFileSizeMB, results, stats)
}

// StreamGitHubEvents connects to GitHub's event firehose and streams commit events
func StreamGitHubEvents(ctx context.Context, client *github.Client, allCommits bool, results chan<- []CommitEvent, stats *StreamStats) error {
	return streamGitHubEventsInternal(ctx, client, allCommits, false, 50, results, stats) // Default 50MB
}

// streamGitHubEventsInternal is the internal implementation for streaming GitHub events
func streamGitHubEventsInternal(ctx context.Context, client *github.Client, allCommits bool, fetchFiles bool, maxFileSizeMB int, results chan<- []CommitEvent, stats *StreamStats) error {

	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
			// Continue processing
		}

		opt := &github.ListOptions{PerPage: GithubDefaultPagination}

		for {
			select {
			case <-ctx.Done():
				return ctx.Err()
			default:
				// Continue processing
			}

			events, resp, err := client.Activity.ListEvents(ctx, opt)

			if handled := handleRateLimit(err); handled {
				continue
			}

			if err != nil {
				log.Error().Msgf("Error fetching GitHub events: %s", err)
				time.Sleep(5 * time.Second)
				continue
			}

			var commits []CommitEvent
			for _, e := range events {
				if *e.Type == "PushEvent" {
					p, _ := e.ParsePayload()
					pushEvent := p.(*github.PushEvent)

					for _, c := range pushEvent.Commits {
						var commit CommitEvent
						commit.Timestamp = time.Now()
						userRepo := *e.GetRepo().Name
						parts := strings.Split(userRepo, "/")
						commit.UserName = parts[0]
						commit.RepoName = parts[1]
						commit.SHA = *c.SHA
						commit.Message = *c.Message
						commit.AuthorName = *c.GetAuthor().Name

						email := *c.GetAuthor().Email

						if strings.Contains(email, "@") {
							parts := strings.Split(email, "@")
							commit.AuthorEmail.User = parts[0]
							commit.AuthorEmail.Domain = parts[1]
						} else {
							commit.AuthorEmail.User = email
						}

						// Fetch file information for the commit if needed
						if fetchFiles {
							modified, added, removed, err := FetchCommitFilesWithSizeLimit(ctx, client, commit.UserName, commit.RepoName, commit.SHA, maxFileSizeMB)
							if err != nil {
								log.Debug().Msgf("Failed to fetch files for commit %s: %v", commit.SHA[:7], err)
							} else {
								commit.ModifiedFiles = modified
								commit.AddedFiles = added
								commit.RemovedFiles = removed
							}
						}

						stats.IncrementIncoming()
						commits = append(commits, commit)

						if !allCommits {
							break
						}
					}
				}
			}

			if len(commits) > 0 {
				results <- commits
			}

			if resp.NextPage == 0 {
				break
			}

			opt.Page = resp.NextPage

			// Rate limit our pagination requests
			time.Sleep(time.Second * time.Duration(GithubPaginationRateSeconds))
		}

		// Wait before starting the next polling cycle
		time.Sleep(time.Second * time.Duration(GithubPollingRateSeconds))
	}
}

var (
	rateLimitMu         sync.RWMutex // Mutex for rate limit state
	rateLimitResumeTime time.Time    // Time to resume after rate limit
	backoffAttempts     int          // Number of backoff attempts
)

// handleRateLimit handles GitHub API rate limiting by sleeping when limits are hit
// Returns true if rate limit was handled, false otherwise
func handleRateLimit(err error) bool {
	// Fast path for no error
	if err == nil {
		// Reset backoff on successful requests
		rateLimitMu.Lock()
		backoffAttempts = 0
		rateLimitMu.Unlock()
		return false
	}

	now := time.Now()

	// Check if we're still in waiting period (read-only operation)
	rateLimitMu.RLock()
	resumeTime := rateLimitResumeTime
	rateLimitMu.RUnlock()

	// If we're still in the waiting period, sleep and retry
	if !resumeTime.IsZero() && now.Before(resumeTime) {
		time.Sleep(time.Until(resumeTime))
		return true
	}

	// Determine retry parameters based on error type (without holding lock)
	var retryAfter time.Duration
	var limitType string
	var newBackoffAttempts int

	var rateLimit *github.RateLimitError
	var abuseLimit *github.AbuseRateLimitError
	var httpErr *github.ErrorResponse

	if errors.As(err, &rateLimit) {
		limitType = "primary"
		if rateLimit.Rate.Remaining == 0 {
			retryAfter = rateLimit.Rate.Reset.Sub(now)
		}
	} else if errors.As(err, &abuseLimit) {
		limitType = "secondary"
		retryAfter = abuseLimit.GetRetryAfter()
	} else if errors.As(err, &httpErr) && (httpErr.Response.StatusCode >= 500 || httpErr.Response.StatusCode == 429) {
		limitType = "transient"

		// Get current backoff attempts (read-only)
		rateLimitMu.RLock()
		newBackoffAttempts = backoffAttempts + 1
		rateLimitMu.RUnlock()

		// Exponential backoff: 2^n * base time (capped)
		backoffSeconds := math.Min(math.Pow(2, float64(newBackoffAttempts)), 300) // Cap at 5 minutes
		retryAfter = time.Duration(backoffSeconds) * time.Second
	} else {
		return false
	}

	// Add jitter to prevent thundering herd
	jitter := time.Duration(rand.IntN(10)+1) * time.Second
	retryAfter = retryAfter + jitter

	// Ensure minimum retry time
	if retryAfter <= 0 {
		retryAfter = (5 * time.Minute) + jitter
	}

	// Update shared state (with lock)
	rateLimitMu.Lock()
	if limitType == "transient" {
		backoffAttempts = newBackoffAttempts
	}
	rateLimitResumeTime = now.Add(retryAfter)
	rateLimitMu.Unlock()

	// Log outside of lock
	log.Warn().Msgf("Exceeded %s rate limit, retry_after: %s, resume_time: %s",
		limitType, retryAfter.String(), rateLimitResumeTime.Format(time.RFC3339))

	// Sleep outside of any locks
	time.Sleep(retryAfter)
	return true
}
