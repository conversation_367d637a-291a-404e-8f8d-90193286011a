package core

import (
	"os"
	"testing"
	"time"
)

func TestSlackNotification(t *testing.T) {
	// Skip test if running in CI environment or if explicitly skipping integration tests
	if os.Getenv("CI") != "" || os.Getenv("SKIP_INTEGRATION_TESTS") != "" {
		t.Skip("Skipping integration test in CI environment")
	}

	// Get webhook URL from environment variable or use default for manual testing
	webhookURL := os.Getenv("SLACK_WEBHOOK_URL")
	if webhookURL == "" {
		webhookURL = "*********************************************************************************" // Default for manual testing
		t.Log("Using default webhook URL. Set SLACK_WEBHOOK_URL env var to override.")
	}

	// Create config with webhook URL
	config := &Config{
		SlackWebhook: webhookURL,
	}

	// Create a test notification with timestamp to identify this specific test run
	timestamp := time.Now().Format("2006-01-02 15:04:05 MST")
	finding := &Notification{
		Query:     "armor-code in:login",
		Rule:      "AWS",
		Secret:    "AKIARPHQIQGW5JJRABFP",
		Commit:    "7444c459b9b9c21e41661c16506823024c5d090e",
		Author:    "davex <<EMAIL>>",
		File:      "aws-commands/commands.md",
		Line:      5,
		Timestamp: timestamp, // Use current time to identify this test run
		RepoURL:   "https://github.com/armor-code/aws",
	}

	// Send the actual notification
	t.Logf("Sending real Slack notification with timestamp: %s", timestamp)
	err := SendSlack(config.SlackWebhook, *finding)
	if err != nil {
		t.Fatalf("Failed to send Slack notification: %v", err)
	}

	// Test with invalid webhook URL
	t.Log("Testing with invalid webhook URL")
	err = SendSlack("https://hooks.slack.com/services/invalid/webhook/url", *finding)
	if err == nil {
		t.Error("Expected error with invalid webhook URL, but got nil")
	} else {
		t.Logf("Got expected error with invalid webhook URL: %v", err)
	}

	// Test with empty webhook URL
	t.Log("Testing with empty webhook URL")
	err = SendSlack("", *finding)
	if err == nil {
		t.Error("Expected error with empty webhook URL, but got nil")
	} else {
		t.Logf("Got expected error with empty webhook URL: %v", err)
	}

	t.Logf("Slack notification test completed. Please check the channel for the message with timestamp: %s", timestamp)
}
