package core

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/rs/zerolog/log"
	"github.com/slack-go/slack"
)

// SendSlack sends a notification message to a Slack channel via webhook
// It formats the message with relevant information about the detected secret
// Returns an error if the webhook call fails
func SendSlack(webhook string, message Notification) error {
	// Validate input
	if webhook == "" {
		return fmt.Errorf("empty Slack webhook URL")
	}

	// Redact secret for security if it's a scan notification
	redactedSecret := "*****"
	if message.Type == ScanNotification && len(message.Secret) > 7 {
		redactedSecret = message.Secret[:3] + "***" + message.Secret[len(message.Secret)-3:]
	}

	// Create fields based on notification type
	var fields []slack.AttachmentField
	var pretext string
	var color string
	var ghCodeLink string
	var title string
	var titleLink string
	var thumbURL string

	switch message.Type {
	case ScanNotification:
		// Format for scan notifications
		pretext = "🔔 *Security Alert*"
		title = "Potential Secret Detected"
		color = "#FF0000"                                                                 // Red for scan findings
		thumbURL = "https://github.githubassets.com/images/icons/emoji/unicode/1f510.png" // :closed_lock_with_key: - lock emoji

		// GitHub code link for scans
		if message.File != "" && message.Commit != "" {
			ghCodeLink = fmt.Sprintf("%s/blob/%s/%s#L%d",
				message.RepoURL, message.Commit, message.File, message.Line)
			titleLink = ghCodeLink
		} else {
			ghCodeLink = message.RepoURL
			titleLink = message.RepoURL
		}

		fields = []slack.AttachmentField{
			{
				Title: "Rule",
				Value: fmt.Sprintf("`%s`", message.Rule),
				Short: true,
			},
			{
				Title: "Secret",
				Value: fmt.Sprintf("`%s`", redactedSecret),
				Short: true,
			},
			{
				Title: "File",
				Value: fmt.Sprintf("`%s`", message.File),
				Short: true,
			},
			{
				Title: "Author",
				Value: message.Author,
				Short: true,
			},
			{
				Title: "Commit",
				Value: fmt.Sprintf("<%s/commit/%s|`%s`>",
					message.RepoURL, message.Commit, message.Commit[:7]),
				Short: true,
			},
			{
				Title: "Repository",
				Value: fmt.Sprintf("<%s|%s>",
					message.RepoURL, message.RepoURL),
				Short: true,
			},
			{
				Title: "Timestamp",
				Value: message.Timestamp,
				Short: true,
			},
		}

		// Add dangler information if it's a dangling commit
		if message.DanglerCommit {
			fields = append(fields, slack.AttachmentField{
				Title: "⛳ Dangling Commit",
				Value: "This secret was found in a dangling commit",
				Short: false,
			})
		}

	case WatchNotification:
		// Format for watch notifications
		pretext = "👀 *Commit Watch*"
		title = "Watched Commit Detected"
		color = "#FFA500"                                                                 // Orange for watch findings
		thumbURL = "https://github.githubassets.com/images/icons/emoji/unicode/1f4e1.png" // :satellite_antenna: - satellite antenna emoji

		// GitHub commit link for watches
		ghCodeLink = fmt.Sprintf("%s/commit/%s",
			message.RepoURL, message.Commit)
		titleLink = ghCodeLink

		fields = []slack.AttachmentField{
			{
				Title: "Author",
				Value: message.Author,
				Short: true,
			},
			{
				Title: "Commit",
				Value: fmt.Sprintf("<%s/commit/%s|`%s`>",
					message.RepoURL, message.Commit, message.Commit[:7]),
				Short: true,
			},
			{
				Title: "Repository",
				Value: fmt.Sprintf("<%s|%s>",
					message.RepoURL, message.RepoURL),
				Short: true,
			},
			{
				Title: "Timestamp",
				Value: message.Timestamp,
				Short: true,
			},
			{
				Title: "Watch Filter",
				Value: fmt.Sprintf("`%s`", message.Query),
				Short: false,
			},
		}

		// Add commit message if available
		if message.CommitMsg != "" {
			fields = append(fields, slack.AttachmentField{
				Title: "Commit Message",
				Value: fmt.Sprintf("```%s```", message.CommitMsg),
				Short: false,
			})
		}
	}

	// Create fallback text
	var fallback string
	if message.Type == ScanNotification {
		fallback = fmt.Sprintf(
			"Octopi found %s secret committed by %s",
			message.Rule, message.Author,
		)
	} else {
		fallback = fmt.Sprintf(
			"Octopi found matching commit by %s",
			message.Author,
		)
	}

	// Create message attachment with all details
	attachment := slack.Attachment{
		Color:      color,
		Pretext:    pretext,
		Title:      title,
		TitleLink:  titleLink,
		ThumbURL:   thumbURL,
		Fallback:   fallback,
		Fields:     fields,
		MarkdownIn: []string{"text", "pretext", "fields"},
		Actions: []slack.AttachmentAction{
			{
				Type:  "button",
				Style: "primary",
				Text:  "View on GitHub",
				URL:   ghCodeLink,
			},
		},
		Footer:     fmt.Sprintf("Octopi v%s", Version),
		FooterIcon: "https://github.githubassets.com/images/modules/logos_page/GitHub-Mark.png",
		Ts:         json.Number(strconv.FormatInt(time.Now().Unix(), 10)),
	}

	// Prepare and send the message
	msg := slack.WebhookMessage{
		Attachments: []slack.Attachment{attachment},
	}

	log.Debug().Msgf("Sending Slack notification for rule: %s", message.Rule)
	err := slack.PostWebhook(webhook, &msg)
	if err != nil {
		return err
	}

	log.Debug().Msgf("Successfully sent Slack notification for rule: %s", message.Rule)
	return nil
}
