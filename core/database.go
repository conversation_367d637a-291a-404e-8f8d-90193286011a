package core

import (
	"errors"
	"fmt"
	"sync"
	"time"

	"github.com/rs/zerolog/log"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/logger"
)

// DBManager provides thread-safe access to the database
type DBManager struct {
	db    *gorm.DB
	mutex sync.RWMutex
}

// Global instance of the database manager
var dbManager *DBManager
var databasePath = GetConfigFilePath("octopi.db")

var scannerType = map[int]string{
	0: "Gitleaks",
	1: "Trufflehog",
}

// InitOctopiDB initializes the database
func InitOctopiDB() {
	db, err := gorm.Open(sqlite.Open(databasePath), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	if err != nil {
		log.Fatal().Msgf("%s", err.<PERSON>rror())
	}
	err = db.AutoMigrate(&Octopi{})
	if err != nil {
		log.Fatal().Msgf("%s", err.Error())
	}
	dbManager = &DBManager{
		db: db,
	}
}

// AddOctos adds the repos to the database
func AddOctos(octos []Octopi) error {
	dbManager.mutex.Lock()
	defer dbManager.mutex.Unlock()

	var recordCount = 0
	for _, octo := range octos {
		exists, err := CheckRepoExists(octo.RepoURL)
		if !exists {
			recordCount++
		} else {
			if err != nil {
				log.Error().Msgf("%s", err.Error())
			} else {
				log.Debug().Msgf("Repository already exist in database: %s/%s", octo.Owner, octo.RepoName)
			}
			continue
		}
		repoDBEntry := &Octopi{
			RepoName: octo.RepoName,
			RepoURL:  octo.RepoURL,
			RepoSize: octo.RepoSize,
			Owner:    octo.Owner,
			Query:    octo.Query,
		}
		res := dbManager.db.Clauses(clause.OnConflict{DoNothing: true}).Create(&repoDBEntry)
		if res.Error != nil {
			return fmt.Errorf("failed to add Octo to database: %s", res.Error.Error())
		}
	}
	log.Info().Msgf("Added %d Octo(s) to database", recordCount)
	return nil
}

// GetOctoCount gets the number of repos stored in database
func GetOctoCount() (int64, error) {
	dbManager.mutex.RLock()
	defer dbManager.mutex.RUnlock()

	var count int64
	err := dbManager.db.Model(&Octopi{}).Count(&count).Error
	if err != nil {
		return 0, fmt.Errorf("failed to get octo count from database: %s", err.Error())
	}
	return count, nil
}

// GetOctos gets the repos stored in database based on the update parameter
// If skipFailed is true, repositories with SCAN_FAILED commit SHA will be excluded
func GetOctos(virgin bool, skipFailed bool) ([]Octopi, error) {
	dbManager.mutex.RLock()
	defer dbManager.mutex.RUnlock()

	var octos []Octopi
	query := dbManager.db

	if skipFailed {
		// Exclude repositories that have failed scanning
		query = query.Where("scan_gitleaks_commit <> ? OR scan_gitleaks_commit IS NULL", "SCAN_FAILED").
			Where("scan_trufflehog_commit <> ? OR scan_trufflehog_commit IS NULL", "SCAN_FAILED")
	}

	if virgin {
		// OctopiDB.Where("scanned = ?", false).Find(&searchHistoryItems)
		err := query.Where(
			"scan_gitleaks_date = ?", time.Time{}).Or(
			"scan_trufflehog_date = ?", time.Time{}).Find(&octos).Error
		if err != nil {
			return nil, fmt.Errorf("failed to get octos from database: %s", err.Error())
		}
	} else {
		query.Find(&octos)
	}
	return octos, nil
}

// UpdateOcto updates the last committer information for a repository
func UpdateOcto(repoUrl string, latestCommit *Commit) {
	dbManager.mutex.Lock()
	defer dbManager.mutex.Unlock()

	var repoRecord Octopi
	err := dbManager.db.First(&repoRecord, "repo_url = ?", repoUrl).Error
	if err != nil {
		log.Error().Msgf("%s", err.Error())
		return
	}
	lastCommitter := &Octopi{
		Committer: LastCommitter{
			User:  latestCommit.User,
			Email: latestCommit.Email,
		},
	}
	err = dbManager.db.Model(Octopi{}).Where("repo_url = ?", repoUrl).Updates(lastCommitter).Error
	if err != nil {
		log.Error().Msgf("%s", err.Error())
		return
	}
}

// DeleteOcto deletes a repository from the database
func DeleteOcto(repoUrl string) {
	dbManager.mutex.Lock()
	defer dbManager.mutex.Unlock()

	var repoRecord Octopi
	err := dbManager.db.First(&repoRecord, "repo_url = ?", repoUrl).Error
	if err != nil {
		log.Error().Msgf("%s", err.Error())
		return
	}
	dbManager.db.Unscoped().Delete(&repoRecord)
	log.Debug().Msgf("Octo database record deleted")
}

// UpdateOctoScan updates the last scanned commit information for a repository
func UpdateOctoScan(repoUrl string, reporter Reporter, latestCommit *Commit) error {
	dbManager.mutex.Lock()
	defer dbManager.mutex.Unlock()

	var repoRecord Octopi
	err := dbManager.db.First(&repoRecord, "repo_url = ?", repoUrl).Error
	if err != nil {
		return fmt.Errorf("failed to find octo record in database: %s", err.Error())
	}
	var latestScanCommit *Octopi

	switch reporter {
	case GitleaksScanner:
		latestScanCommit = &Octopi{
			ScanStatus: ScanStatus{
				Gitleaks: LastScanned{
					Commit: latestCommit.SHA,
					Date:   latestCommit.Date,
				},
			},
		}
	case TrufflehogScanner:
		latestScanCommit = &Octopi{
			ScanStatus: ScanStatus{
				Trufflehog: LastScanned{
					Commit: latestCommit.SHA,
					Date:   latestCommit.Date,
				},
			},
		}
	}
	err = dbManager.db.Model(Octopi{}).Where("repo_url = ?", repoUrl).Updates(latestScanCommit).Error
	if err != nil {
		return fmt.Errorf("unable to update octo database record: %s", err.Error())
	}
	return nil
}

// CheckRepoExists checks if a repository exists in the database
func CheckRepoExists(repo string) (exists bool, err error) {
	dbManager.mutex.RLock()
	defer dbManager.mutex.RUnlock()

	var repository Octopi
	res := dbManager.db.Where("repo_url = ?", repo).First(&repository)
	if res.Error != nil {
		if errors.Is(res.Error, gorm.ErrRecordNotFound) {
			return false, nil
		}
		return false, res.Error
	}
	return true, nil
}

// UpdateOctoURL updates the repository URL in the database
func UpdateOctoURL(id uint, newURL string) error {
	dbManager.mutex.Lock()
	defer dbManager.mutex.Unlock()

	err := dbManager.db.Model(&Octopi{}).Where("id = ?", id).Update("repo_url", newURL).Error
	if err != nil {
		return fmt.Errorf("failed to update repository URL in database: %s", err.Error())
	}

	return nil
}

// UpdateOctoScanFailure marks a repository as having failed scanning with a specific scanner
func UpdateOctoScanFailure(repoUrl string, reporter Reporter, reason string) error {
	dbManager.mutex.Lock()
	defer dbManager.mutex.Unlock()

	var repoRecord Octopi
	err := dbManager.db.First(&repoRecord, "repo_url = ?", repoUrl).Error
	if err != nil {
		return fmt.Errorf("failed to find octo record in database: %s", err.Error())
	}

	// Create a dummy commit to mark the failure
	failureCommit := &Commit{
		SHA: "SCAN_FAILED",
		// User:  "system",
		// Email: "<EMAIL>",
		Date: time.Now().UTC(),
	}

	// Update the database with failure information
	var scanFailureRecord *Octopi

	switch reporter {
	case GitleaksScanner:
		scanFailureRecord = &Octopi{
			ScanStatus: ScanStatus{
				Gitleaks: LastScanned{
					Commit: failureCommit.SHA,
					Date:   failureCommit.Date,
				},
			},
		}
	case TrufflehogScanner:
		scanFailureRecord = &Octopi{
			ScanStatus: ScanStatus{
				Trufflehog: LastScanned{
					Commit: failureCommit.SHA,
					Date:   failureCommit.Date,
				},
			},
		}
	}

	err = dbManager.db.Model(Octopi{}).Where("repo_url = ?", repoUrl).Updates(scanFailureRecord).Error
	if err != nil {
		return fmt.Errorf("unable to update octo scan failure record: %s", err.Error())
	}

	// Log the failure reason
	log.Warn().Msgf("Repository %s marked as failed for scanner %s: %s",
		repoUrl, scannerType[int(reporter)], reason)

	return nil
}

// HasScanFailed checks if a repository has failed scanning with a specific scanner
func HasScanFailed(repoUrl string, reporter Reporter) (bool, error) {
	dbManager.mutex.RLock()
	defer dbManager.mutex.RUnlock()

	var repoRecord Octopi
	err := dbManager.db.First(&repoRecord, "repo_url = ?", repoUrl).Error
	if err != nil {
		return false, fmt.Errorf("failed to find octo record in database: %s", err.Error())
	}

	switch reporter {
	case GitleaksScanner:
		return repoRecord.ScanStatus.Gitleaks.Commit == "SCAN_FAILED", nil
	case TrufflehogScanner:
		return repoRecord.ScanStatus.Trufflehog.Commit == "SCAN_FAILED", nil
	default:
		return false, fmt.Errorf("unknown scanner type: %d", reporter)
	}
}
