package core

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"os"
	"os/exec"
	"path"
	"path/filepath"
	"strings"
	"time"

	"github.com/rs/zerolog/log"
)

// Gitleaks represents the configuration for the Gitleaks scanner
type Gitleaks struct {
	Enabled            bool     // Whether to enable the Gitleaks tool
	Config             string   // Path to Gitleaks config file
	MaxTargetMegaBytes string   // Maximum file size to scan. files larger than this will be skipped
	EnabledRules       []string // List of Gitleaks rules to enable
	SkipDuplicates     bool     // Whether to skip duplicate secrets
	SpecificCommits    []string // List of specific commits to scan
	SpecificBranches   []string // List of specific branches to scan
	SinceDate          string   // Start date for scanning (format: YYYY-MM-DD)
	UntilDate          string   // End date for scanning (format: YYYY-MM-DD)
}

// GitleaksFinding represents the structure of a single finding in the Gitleaks JSON report
type GitleaksFinding struct {
	RepoURL       string   `json:"repo_url"`       // URL of the repository
	RuleID        string   `json:"rule_id"`        // ID of the rule that triggered the finding
	Description   string   `json:"description"`    // Description of the finding
	StartLine     int64    `json:"start_line"`     // Start line of the finding
	Match         string   `json:"match"`          // Matched secret
	Secret        string   `json:"secret"`         // Secret (usually the same as match)
	File          string   `json:"file"`           // File containing the finding
	Commit        string   `json:"commit"`         // Commit containing the finding
	Entropy       float64  `json:"entropy"`        // Entropy of the secret
	Author        string   `json:"author"`         // Author of the commit
	Email         string   `json:"email"`          // Email of the author
	Link          string   `json:"link"`           // Link to the finding on GitHub
	Date          string   `json:"date"`           // Date of the commit
	Message       string   `json:"message"`        // Commit message
	Tags          []string `json:"tags"`           // Tags associated with the finding
	Fingerprint   string   `json:"fingerprint"`    // Fingerprint of the finding
	DanglerCommit bool     `json:"dangler_commit"` // Whether this is a dangling commit
	Severity      string   `json:"severity"`       // Severity of the finding
}

// GitleaksMapping represents the structure of a single finding in the Gitleaks JSON report
type GitleaksMapping struct {
	RuleID      string   `json:"RuleID"`      // Gitleaks uses PascalCase
	Description string   `json:"Description"` // Gitleaks uses PascalCase
	StartLine   int      `json:"StartLine"`   // Gitleaks uses PascalCase
	EndLine     int      `json:"EndLine"`     // Gitleaks includes EndLine
	StartColumn int      `json:"StartColumn"` // Gitleaks includes StartColumn
	EndColumn   int      `json:"EndColumn"`   // Gitleaks includes EndColumn
	Match       string   `json:"Match"`       // Gitleaks uses PascalCase
	Secret      string   `json:"Secret"`      // Gitleaks uses PascalCase
	File        string   `json:"File"`        // Gitleaks uses PascalCase
	SymlinkFile string   `json:"SymlinkFile"` // Gitleaks includes SymlinkFile
	Commit      string   `json:"Commit"`      // Gitleaks uses PascalCase
	Entropy     float64  `json:"Entropy"`     // Gitleaks uses PascalCase
	Author      string   `json:"Author"`      // Gitleaks uses PascalCase
	Email       string   `json:"Email"`       // Gitleaks uses PascalCase
	Date        string   `json:"Date"`        // Gitleaks uses PascalCase
	Message     string   `json:"Message"`     // Gitleaks uses PascalCase
	Tags        []string `json:"Tags"`        // Gitleaks uses PascalCase
	Fingerprint string   `json:"Fingerprint"` // Gitleaks uses PascalCase
}

// RunGitleaks executes the Gitleaks tool on a specified repository path with the provided configuration.
func (s *Scan) RunGitleaks(ctx context.Context, octo Octopi, repoPath string) ([]GitleaksFinding, error) {
	reportPath := path.Join(repoPath, "gitleaks.json")

	// Build command arguments
	cliArgs := buildGitleaksArgs(s, octo, repoPath, reportPath)

	// Add timeout handling to the context
	scanCtx, cancel := context.WithTimeout(ctx, 30*time.Minute)
	defer cancel()

	// Execute Gitleaks command
	findings, err := executeGitleaksCommand(scanCtx, cliArgs, reportPath)
	if err != nil {
		return nil, err
	}

	// Handle empty results more gracefully
	if len(findings) == 0 {
		log.Debug().Str("repo", repoPath).Msg("No Gitleaks findings detected")
		return []GitleaksFinding{}, nil
	}

	// Map findings to GitleaksFindings
	results := mapGitleaksResults(findings, octo)

	// Filter out duplicate secrets if configured
	if s.Gitleaks.SkipDuplicates {
		return filterDuplicateGitleaksSecrets(results), nil
	}

	return results, nil
}

// buildGitleaksArgs constructs the command line arguments for Gitleaks
func buildGitleaksArgs(s *Scan, octo Octopi, repoPath, reportPath string) []string {
	cliArgs := []string{
		"git",
		"--no-banner",
		"--no-color",
		"--exit-code=0",
		"--log-level=error",
		"--report-format=json",
		"--report-path=" + reportPath,
	}

	// Add concurrency setting if specified (greater than 0)
	if s.Concurrency > 0 {
		cliArgs = append(cliArgs, fmt.Sprintf("--threads=%d", s.Concurrency))
	}

	// Add configuration file if specified
	if s.Gitleaks.Config != "" {
		// Expand tilde in config path
		expandedConfigPath, err := ExpandPath(s.Gitleaks.Config)
		if err != nil {
			log.Warn().Msgf("Failed to expand config path %s: %v", s.Gitleaks.Config, err)
			expandedConfigPath = s.Gitleaks.Config // Fall back to original path
		}
		cliArgs = append(cliArgs, "--config", expandedConfigPath)
	}

	// Add max target size if specified
	if s.Gitleaks.MaxTargetMegaBytes != "0" {
		cliArgs = append(cliArgs, "--max-target-megabytes", s.Gitleaks.MaxTargetMegaBytes)
	}

	// Check for baseline file
	gitleaksBaselinePath := filepath.Join(repoPath, ".gitleaksbaseline")
	if CheckFileExists(gitleaksBaselinePath) {
		cliArgs = append(cliArgs, "--baseline-path", gitleaksBaselinePath)
	}

	// Check for ignore file
	gitleaksIgnorePath := filepath.Join(repoPath, ".gitleaksignore")
	if CheckFileExists(gitleaksIgnorePath) {
		cliArgs = append(cliArgs, "--gitleaks-ignore-path", gitleaksIgnorePath)
	}

	// Add enabled rules if specified
	if len(s.Gitleaks.EnabledRules) != 0 {
		for _, r := range s.Gitleaks.EnabledRules {
			cliArgs = append(cliArgs, fmt.Sprintf("--enable-rule=%s", r))
		}
	}

	// Add specific commits or branches if specified
	if len(s.Gitleaks.SpecificCommits) > 0 {
		for _, commit := range s.Gitleaks.SpecificCommits {
			cliArgs = append(cliArgs, "--commit="+commit)
		}
	} else if len(s.Gitleaks.SpecificBranches) > 0 {
		for _, branch := range s.Gitleaks.SpecificBranches {
			cliArgs = append(cliArgs, "--branch="+branch)
		}
	} else if s.UpdateRepos && !s.ForceRescan {
		// Incremental scan - use full history and all branches, but only after the last scan date
		gitLogOpts := []string{"--full-history", "--all", "--after="}
		gitLogOpts = append(gitLogOpts, octo.ScanStatus.Gitleaks.Date.Format("2006-01-02T15:04:05.000Z"))
		cliArgs = append(cliArgs, fmt.Sprintf("--log-opts=%s", strings.Join(gitLogOpts, " ")))
	} else if s.Dangler.Enabled {
		// If dangler is enabled, we need to scan all branches to catch dangling commits
		gitLogOpts := []string{"--full-history", "--all"}
		cliArgs = append(cliArgs, fmt.Sprintf("--log-opts=%s", strings.Join(gitLogOpts, " ")))
	}

	// Add repository path
	cliArgs = append(cliArgs, repoPath)

	return cliArgs
}

// executeGitleaksCommand runs the Gitleaks command and parses the results
func executeGitleaksCommand(ctx context.Context, cliArgs []string, reportPath string) ([]GitleaksMapping, error) {
	cmd := exec.CommandContext(ctx, GetCommandPath("gitleaks"), cliArgs...)
	log.Debug().Msgf("Executing Gitleaks command: %s", cmd.String())

	// Capture stderr
	stderrP, err := cmd.StderrPipe()
	if err != nil {
		return nil, fmt.Errorf("failed to create stderr pipe: %w", err)
	}

	// Start the command
	if err := cmd.Start(); err != nil {
		return nil, fmt.Errorf("failed to start Gitleaks: %w", err)
	}

	// Read stderr
	stderr, err := io.ReadAll(stderrP)
	if err != nil {
		return nil, fmt.Errorf("failed to read stderr: %w", err)
	}

	// Wait for command to complete
	if err := cmd.Wait(); err != nil {
		var exitError *exec.ExitError
		if errors.As(err, &exitError) {
			return nil, fmt.Errorf("Gitleaks exited with error (code %d):\n%s",
				exitError.ExitCode(), stderr)
		}
		return nil, fmt.Errorf("error waiting for Gitleaks: %w", err)
	}

	// Parse results
	return parseGitleaksResults(reportPath)
}

// parseGitleaksResults reads and parses the Gitleaks JSON output file
func parseGitleaksResults(reportPath string) ([]GitleaksMapping, error) {
	// Check if report file exists
	if !CheckFileExists(reportPath) {
		return nil, fmt.Errorf("Gitleaks report file not found: %s", reportPath)
	}

	// Open the report file
	f, err := os.Open(reportPath)
	if err != nil {
		return nil, fmt.Errorf("failed to open Gitleaks report: %w", err)
	}
	defer f.Close()

	// Decode the JSON
	decoder := json.NewDecoder(f)
	var gitleaksFindings []GitleaksMapping

	if err := decoder.Decode(&gitleaksFindings); err != nil {
		return nil, fmt.Errorf("unable to decode Gitleaks JSON results: %w", err)
	}

	return gitleaksFindings, nil
}

// mapGitleaksResults converts GitleaksMapping to GitleaksFinding
func mapGitleaksResults(reports []GitleaksMapping, octo Octopi) []GitleaksFinding {
	var findings []GitleaksFinding
	for _, f := range reports {
		fLink := fmt.Sprintf("%s/blob/%s/%s#L%d", octo.RepoURL, f.Commit, f.File, f.StartLine)

		// Initialize isDangler to false
		// The actual dangling commit check happens in scan.go with IsDanglingCommit()
		isDangler := false

		// Determine severity based on rule ID or other factors
		severity := determineGitleaksSeverity(f.RuleID, f.Description)

		findings = append(findings, GitleaksFinding{
			RuleID:        f.RuleID,
			Description:   f.Description,
			Match:         f.Match,
			Secret:        f.Secret,
			File:          f.File,
			StartLine:     int64(f.StartLine),
			Commit:        f.Commit,
			Entropy:       float64(f.Entropy),
			Author:        f.Author,
			Email:         f.Email,
			Link:          fLink,
			Date:          f.Date,
			Tags:          f.Tags,
			Fingerprint:   f.Fingerprint,
			DanglerCommit: isDangler,
			Severity:      severity,
		})
	}
	return findings
}

// filterDuplicateGitleaksSecrets removes duplicate findings based on the raw secret value
// It keeps the most recent finding for each unique secret
func filterDuplicateGitleaksSecrets(findings []GitleaksFinding) []GitleaksFinding {
	uniqueSecrets := make(map[string]GitleaksFinding)

	// Process all findings
	for _, finding := range findings {
		// Create a unique key based on the secret and rule ID
		// This allows different types of secrets with the same value to be reported separately
		key := finding.Secret + "|" + finding.RuleID

		// Check if we've seen this secret before
		existingFinding, exists := uniqueSecrets[key]

		// If we haven't seen it, or this is a newer commit, keep this one
		if !exists || finding.Date > existingFinding.Date {
			uniqueSecrets[key] = finding
		}
	}

	// Convert the map back to a slice
	var result []GitleaksFinding
	for _, finding := range uniqueSecrets {
		result = append(result, finding)
	}

	return result
}

// determineSeverity assigns a severity level based on the rule ID or description
func determineGitleaksSeverity(ruleID, description string) string {
	// High severity rules
	highSeverityRules := map[string]bool{
		"aws-access-token": true,
		"github-pat":       true,
		"private-key":      true,
		// Add more high severity rules
	}

	if highSeverityRules[ruleID] {
		return "high"
	}

	// Check description for keywords indicating high severity
	highSeverityKeywords := []string{"password", "token", "key", "secret", "credential"}
	for _, keyword := range highSeverityKeywords {
		if strings.Contains(strings.ToLower(description), keyword) {
			return "high"
		}
	}

	return "medium" // Default severity
}
