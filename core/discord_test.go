package core

import (
    "os"
    "testing"
    "time"
)

func TestDiscordNotification(t *testing.T) {
    // Skip test if running in CI environment or if explicitly skipping integration tests
    if os.Getenv("CI") != "" || os.Getenv("SKIP_INTEGRATION_TESTS") != "" {
        t.Skip("Skipping integration test in CI environment")
    }

    // Get webhook from environment variable
    webhook := os.Getenv("DISCORD_WEBHOOK")
    if webhook == "" {
        t.Skip("DISCORD_WEBHOOK environment variable not set, skipping test")
    }

    // Create a test notification
    notification := Notification{
        Type:      ScanNotification,
        Rule:      "discord-api-token",
        Secret:    "abcdefghijklmnopqrstuvwxyz1234567890",
        Commit:    "0123456789abcdef0123456789abcdef01234567",
        Author:    "<EMAIL>",
        File:      "config/secrets.yaml",
        Line:      42,
        Timestamp: time.Now().Format(time.RFC3339),
        RepoURL:   "https://github.com/hologram/octopi",
    }

    // Send the notification
    err := SendDiscord(webhook, notification)
    if err != nil {
        t.Fatalf("Failed to send Discord notification: %v", err)
    }
}