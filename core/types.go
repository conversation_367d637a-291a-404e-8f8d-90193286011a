package core

import (
	"context"
	"net/http"
	"sync"
	"time"

	"github.com/google/go-github/v72/github"
	"github.com/shurcooL/githubv4"
)

// Config represents the configuration for Octopi.
type Config struct {
	GithubPAT      string                  `yaml:"github_pat,omitempty"`       // GitHub Personal Access Token
	SlackWebhook   string                  `yaml:"slack_webhook,omitempty"`    // Slack webhook URL
	TelegramBotId  string                  `yaml:"telegram_botid,omitempty"`   // Bot ID for Telegram notifications
	TelegramChatId int64                   `yaml:"telegram_chatid,omitempty"`  // Chat ID for Telegram notifications
	DiscordWebhook string                  `yaml:"discord_webhook,omitempty"`  // Discord webhook URL
	GitLeaksConfig string                  `yaml:"gitleaks_config,omitempty"`  // Path to Gitleaks config file
	IgnoreUsers    []string                `yaml:"ignore_users,omitempty"`     // Users to ignore in the format of "username"
	IgnoreRepos    []string                `yaml:"ignore_repos,omitempty"`     // Repositories to ignore in the format of "owner/repo"
	GithubUsername string                  `yaml:"github_username,omitempty"`  // Username for GitHub login (for UI search)
	GithubPassword string                  `yaml:"github_password,omitempty"`  // Password for GitHub login (for UI search)
	GithubTOTPSeed string                  `yaml:"github_totp_seed,omitempty"` // TOTP seed for GitHub login (for UI search)
	WatchProfiles  map[string]WatchProfile `yaml:"watch_profiles,omitempty"`   // Named watch configurations
	DefaultWatch   WatchProfile            `yaml:"default_watch,omitempty"`    // Default watch settings
}

// WatchProfile represents a named watch configuration with multiple filters
type WatchProfile struct {
	Description        string             `yaml:"description,omitempty"`          // Human-readable description
	AuthorNames        []string           `yaml:"author_names,omitempty"`         // Author names to match
	EmailDomains       []string           `yaml:"email_domains,omitempty"`        // Email domains to match
	DomainsFile        string             `yaml:"domains_file,omitempty"`         // File containing email domains
	SensitiveFiles     []string           `yaml:"sensitive_files,omitempty"`      // Sensitive file patterns (auto-enables file fetching)
	SensitivePathsFile string             `yaml:"sensitive_paths_file,omitempty"` // File containing sensitive patterns (auto-enables file fetching)
	IgnorePrivate      bool               `yaml:"ignore_private,omitempty"`       // Ignore GitHub private emails
	AlertOnSensitive   bool               `yaml:"alert_on_sensitive,omitempty"`   // Alert on sensitive file changes
	MaxFileSizeMB      int                `yaml:"max_file_size_mb,omitempty"`     // Maximum file size to fetch (MB)
	SearchAllCommits   bool               `yaml:"search_all_commits,omitempty"`   // Process all commits in push events
	RepoFilters        RepoFilters        `yaml:"repo_filters,omitempty"`         // Repository filtering options
	Notifications      WatchNotifications `yaml:"notifications,omitempty"`        // Notification settings
}

// RepoFilters represents repository-level filtering options
type RepoFilters struct {
	NewRepositories int  `yaml:"new_repositories,omitempty"` // Repos created in last N days
	ForkedRepos     bool `yaml:"forked_repos,omitempty"`     // Include activity on forked repos
	ArchivedRepos   bool `yaml:"archived_repos,omitempty"`   // Include archived repos
	RepoSizeLimit   int  `yaml:"repo_size_limit,omitempty"`  // Repos larger than N MB
	StarCountMin    int  `yaml:"star_count_min,omitempty"`   // Minimum star count
}

// WatchNotifications represents notification settings for a watch profile
type WatchNotifications struct {
	Slack    bool `yaml:"slack,omitempty"`    // Send to Slack
	Telegram bool `yaml:"telegram,omitempty"` // Send to Telegram
	Discord  bool `yaml:"discord,omitempty"`  // Send to Discord
}

// NotificationType represents the category of notification
type NotificationType int

const (
	ScanNotification  NotificationType = iota // Notification for a secret scan finding
	WatchNotification                         // Notification for a watched commit
)

type Notification struct {
	Type          NotificationType `json:"type"`           // Type of notification (scan or watch)
	Query         string           `json:"query"`          // Search query or filter criteria
	Rule          string           `json:"rule"`           // Rule that triggered the notification
	Secret        string           `json:"secret"`         // Secret value (for scans) or commit hash (for watch)
	Commit        string           `json:"commit"`         // Commit hash
	Author        string           `json:"author"`         // Author of the commit
	File          string           `json:"file"`           // File containing the secret (empty for watch)
	Line          int              `json:"line"`           // Line number in file (0 for watch)
	Timestamp     string           `json:"timestamp"`      // Timestamp of the finding
	RepoURL       string           `json:"repo_url"`       // URL of the repository
	CommitMsg     string           `json:"commit_msg"`     // Commit message (for watch)
	DanglerCommit bool             `json:"dangler_commit"` // Whether this is a dangling commit
}

// Reporter represents the type of report being generated
type Reporter int

const (
	Reconnaissance     Reporter = 0
	DanglerScanner     Reporter = 1
	GitleaksScanner    Reporter = 2
	TrufflehogScanner  Reporter = 3
	GitleaksSensitive  Reporter = 4
	TrufflehogVerified Reporter = 5
)

// Scan represents the configuration for a complete repository scanning operation.
// It contains settings for various scanning tools and GitHub API interaction.
type Scan struct {
	ctx context.Context // Context for API requests

	Concurrency int  // Number of threads/workers to use for scanning tools
	UpdateRepos bool // Whether to update local repository copies
	ForceRescan bool // Whether to rescan repositories that have already been scanned

	Octopi     Octopi
	Github     Github     // GitHub API configuration
	Dangler    Dangler    // Dangler tool configuration
	Gitleaks   Gitleaks   // Gitleaks tool configuration
	Trufflehog Trufflehog // Trufflehog tool configuration

	SendSlack    bool // Whether to send results to Slack
	SendTelegram bool // Whether to send results to Telegram
	SendDiscord  bool // Whether to send results to Discord
}

// Github represents the configuration and state for GitHub API interactions.
// It contains settings for repository filtering and clients for API access.
type Github struct {
	Mutex            sync.Mutex             // Mutex for thread-safe operations
	Client           *github.Client         // REST API client
	Cache            *CacheManager          // Cache manager
	CacheTTL         time.Duration          // Cache TTL
	GraphQLClient    *githubv4.Client       // GraphQL API client
	HTTPClient       *http.Client           // HTTP client for Web UI search
	RepoResults      []*github.Repository   // Repository search results
	CodeResults      []*github.CodeResult   // Code search results
	CommitResults    []*github.CommitResult // Commit search results
	MaxRepoSize      int                    // Maximum repository size in MB to clone
	IncludeForks     bool                   // Whether to include forked repositories
	FindContributors bool                   // Whether to find leaked emails in commits
	FindLeakedEmails bool                   // Whether to find repository contributors
}

// Octopi represents a repository that has been collected and is ready for scanning.
type Octopi struct {
	ID         uint `gorm:"primaryKey"`
	RepoName   string
	RepoURL    string `gorm:"uniqueIndex"`
	RepoSize   int
	Owner      string
	Query      string
	ScanStatus ScanStatus    `gorm:"embedded;embeddedPrefix:scan_"`
	Committer  LastCommitter `gorm:"embedded;embeddedPrefix:last_committer"`
}

// ScanStatus represents the last scan information for a repository
type ScanStatus struct {
	Gitleaks   LastScanned `gorm:"embedded;embeddedPrefix:gitleaks_"`
	Trufflehog LastScanned `gorm:"embedded;embeddedPrefix:trufflehog_"`
}

// LastScanned represents the last scan information for a specific scanner
type LastScanned struct {
	Commit string
	Date   time.Time
}

// LastCommitter represents the last committer information for a repository
type LastCommitter struct {
	User  string
	Email string
}

// Commit represents a commit in a repository
type Commit struct {
	SHA   string
	User  string
	Email string
	Date  time.Time
}

// DanglingReport represents a dangling commit finding.
type DanglerReport struct {
	RepoURL string `json:"repo_url"` // URL of the repository
	Commit  string `json:"commit"`   // SHA of the dangling commit
	Tree    string `json:"tree"`     // Tree URL of the dangling commit
}

// ReconReport represents a reconnaissance report for a user or email
type ReconReport struct {
	Type          string `json:"Type"`
	User          string `json:"User,omitempty"`
	Name          string `json:"Name,omitempty"`
	Email         string `json:"Email,omitempty"`
	Source        string `json:"Source"`
	Contributions int    `json:"Contributions"`
}
