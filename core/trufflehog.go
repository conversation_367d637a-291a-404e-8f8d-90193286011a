package core

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"os/exec"
	"path/filepath"
	"strings"
	"time"

	"github.com/rs/zerolog/log"
)

type Trufflehog struct {
	Enabled             bool // Whether to enable the Trufflehog tool
	OnlyVerified        bool // Whether to only output verified findings
	NoVerification      bool // Whether to skip verification of findings
	FilterUnverified    bool // Whether to output only first unverified result per chunk per detector
	OnlyCustomVerifiers bool //
	IncludeDetectors    []string
	ExcludeDetectors    []string
	SkipDuplicates      bool
	MaxDepth            int
	SpecificCommits     []string
	SpecificBranches    []string
	SinceDate           string
	UntilDate           string
}

type TrufflehogFinding struct {
	RepoURL           string            `json:"repo_url"`
	DetectorName      string            `json:"detector_name"`
	DecoderName       string            `json:"decoder_name"`
	Verified          bool              `json:"verified"`
	VerificationError string            `json:"verification_error,omitempty"`
	Raw               string            `json:"raw"`
	RawV2             string            `json:"RawV2,omitempty"`
	Commit            string            `json:"commit"`
	File              string            `json:"file"`
	Email             string            `json:"email"`
	Line              int               `json:"line"`
	Link              string            `json:"link"`
	Timestamp         string            `json:"timestamp"`
	ExtraData         map[string]string `json:"ExtraData,omitempty"`
	DanglerCommit     bool              `json:"dangler_commit"`
	Severity          string            `json:"severity"`
}

type TrufflehogMapping struct {
	SourceMetadata struct {
		Data struct {
			Git struct {
				Commit     string `json:"commit"`
				File       string `json:"file"`
				Email      string `json:"email"`
				Repository string `json:"repository"`
				Timestamp  string `json:"timestamp"`
				Line       int    `json:"line"`
			} `json:"Git"`
		} `json:"Data"`
	} `json:"SourceMetadata"`
	SourceID          int               `json:"SourceID"`
	SourceType        int               `json:"SourceType"`
	SourceName        string            `json:"SourceName"`
	DetectorType      int               `json:"DetectorType"`
	DetectorName      string            `json:"DetectorName"`
	DecoderName       string            `json:"DecoderName"`
	Verified          bool              `json:"Verified"`
	VerificationError string            `json:"VerificationError,omitempty"`
	Raw               string            `json:"Raw"`
	RawV2             string            `json:"RawV2,omitempty"`
	Redacted          string            `json:"Redacted"`
	ExtraData         map[string]string `json:"ExtraData,omitempty"`
}

func (s *Scan) RunTrufflehog(ctx context.Context, octo Octopi, repoPath string) ([]TrufflehogFinding, error) {
	// Build command arguments
	cliArgs := buildTrufflehogArgs(s, octo, repoPath)

	// Add timeout handling to the context
	scanCtx, cancel := context.WithTimeout(ctx, 30*time.Minute)
	defer cancel()

	// Execute Trufflehog command
	findings, err := executeTrufflehogCommand(scanCtx, cliArgs)
	if err != nil {
		return nil, err
	}

	// Handle empty results more gracefully
	if len(findings) == 0 {
		log.Debug().Str("repo", repoPath).Msg("No Trufflehog findings detected")
		return []TrufflehogFinding{}, nil
	}

	// Map the findings
	results := mapTrufflehogResults(findings, octo)

	// Filter out duplicate secrets
	return filterDuplicateTrufflehogSecrets(results), nil
}

// buildTrufflehogArgs constructs the command line arguments for Trufflehog
func buildTrufflehogArgs(s *Scan, octo Octopi, repoPath string) []string {
	cliArgs := []string{
		"git",
		"--json",
		"--no-fail",
		"--no-update",
		"--force-skip-binaries",
	}

	// Add max depth if specified
	if s.Trufflehog.MaxDepth > 0 {
		cliArgs = append(cliArgs, "--max-depth", fmt.Sprintf("%d", s.Trufflehog.MaxDepth))
	}

	// Add concurrency setting if specified (greater than 0)
	if s.Concurrency > 0 {
		cliArgs = append(cliArgs, fmt.Sprintf("--concurrency=%d", s.Concurrency))
	}

	// Check for ignore file
	trufflehogIgnorePath := filepath.Join(repoPath, ".trufflehogignore")
	if CheckFileExists(trufflehogIgnorePath) {
		cliArgs = append(cliArgs, "--exclude-paths", trufflehogIgnorePath)
	}

	cliArgs = append(cliArgs, fmt.Sprintf("file://%s", repoPath))

	// Add verification options
	if s.Trufflehog.OnlyVerified {
		cliArgs = append(cliArgs, "--only-verified")
	}
	if s.Trufflehog.NoVerification {
		cliArgs = append(cliArgs, "--no-verification")
	}
	if s.Trufflehog.FilterUnverified {
		cliArgs = append(cliArgs, "--filter-unverified")
	}

	// Add detector filters
	if len(s.Trufflehog.IncludeDetectors) != 0 {
		cliArgs = append(cliArgs, "--include-detectors")
		cliArgs = append(cliArgs, strings.Join(s.Trufflehog.IncludeDetectors, ","))
	}
	if len(s.Trufflehog.ExcludeDetectors) != 0 {
		cliArgs = append(cliArgs, "--exclude-detectors")
		cliArgs = append(cliArgs, strings.Join(s.Trufflehog.ExcludeDetectors, ","))
	}

	// Add scan options - similar to Gitleaks, always use full history for thoroughness
	if s.UpdateRepos && !s.ForceRescan {
		// For incremental scans, use --since with the last commit
		cliArgs = append(cliArgs, "--since-commit")
		cliArgs = append(cliArgs, octo.ScanStatus.Gitleaks.Commit)
	}

	return cliArgs
}

// executeTrufflehogCommand runs the Trufflehog command and processes the output
func executeTrufflehogCommand(ctx context.Context, cliArgs []string) ([]TrufflehogMapping, error) {
	cmd := exec.CommandContext(ctx, GetCommandPath("trufflehog"), cliArgs...)

	log.Debug().Msgf("Executing Trufflehog command: %s", cmd.String())

	stdoutP, err := cmd.StdoutPipe()
	if err != nil {
		return nil, fmt.Errorf("failed to create stdout pipe: %w", err)
	}
	stderrP, err := cmd.StderrPipe()
	if err != nil {
		return nil, fmt.Errorf("failed to create stderr pipe: %w", err)
	}
	if err := cmd.Start(); err != nil {
		return nil, fmt.Errorf("failed to start trufflehog: %w", err)
	}

	var trufflehogFindings []TrufflehogMapping
	decoder := json.NewDecoder(stdoutP)
	for decoder.More() {
		var i TrufflehogMapping
		if err := decoder.Decode(&i); err != nil {
			return nil, fmt.Errorf("Unable to decode Trufflehog JSON results: %w", err)
		}
		// if "File" is not defined, it means Trufflehog found something in the commit message
		// this is badly handled by Trufflehog at the moment, so let's skip this for now
		if i.SourceMetadata.Data.Git.File == "" {
			continue
		}
		if i.Raw != "" {
			trufflehogFindings = append(trufflehogFindings, i)
		}
	}

	stderr, err := io.ReadAll(stderrP)
	if err != nil {
		return nil, err
	}

	if err := cmd.Wait(); err != nil {
		var exitError *exec.ExitError
		if errors.As(err, &exitError) {
			return nil, fmt.Errorf("Trufflehog exited with error:\n%s", stderr)
		}
		return nil, err
	}

	return trufflehogFindings, nil
}

func mapTrufflehogResults(findings []TrufflehogMapping, octo Octopi) []TrufflehogFinding {
	result := make([]TrufflehogFinding, 0, len(findings))

	for _, f := range findings {
		// Skip empty findings
		if f.Raw == "" || f.SourceMetadata.Data.Git.File == "" {
			continue
		}

		fLink := fmt.Sprintf("%s/blob/%s/%s#L%d",
			octo.RepoURL,
			f.SourceMetadata.Data.Git.Commit,
			f.SourceMetadata.Data.Git.File,
			f.SourceMetadata.Data.Git.Line)

		// Initialize isDangler to false
		// The actual dangling commit check happens in scan.go with IsDanglingCommit()
		isDangler := false

		// Determine severity
		severity := determineTrufflehogSeverity(f.DetectorName, f.Raw)

		result = append(result, TrufflehogFinding{
			DetectorName:      f.DetectorName,
			DecoderName:       f.DecoderName,
			Verified:          f.Verified,
			Raw:               f.Raw,
			VerificationError: f.VerificationError,
			ExtraData:         f.ExtraData,
			Commit:            f.SourceMetadata.Data.Git.Commit,
			File:              f.SourceMetadata.Data.Git.File,
			Line:              f.SourceMetadata.Data.Git.Line,
			Link:              fLink,
			Email:             f.SourceMetadata.Data.Git.Email,
			Timestamp:         f.SourceMetadata.Data.Git.Timestamp,
			RepoURL:           octo.RepoURL,
			DanglerCommit:     isDangler,
			Severity:          severity,
		})
	}

	return result
}

// filterDuplicateTrufflehogSecrets removes duplicate findings based on the raw secret value
// It keeps the most recent finding for each unique secret
func filterDuplicateTrufflehogSecrets(findings []TrufflehogFinding) []TrufflehogFinding {
	// Use a map to track unique secrets
	uniqueSecrets := make(map[string]TrufflehogFinding)

	// Process all findings
	for _, finding := range findings {
		// Create a unique key based on the raw secret and detector name
		key := finding.Raw + "|" + finding.DetectorName

		// Check if we've seen this secret before
		existingFinding, exists := uniqueSecrets[key]

		// If we haven't seen it, or this is a newer commit, keep this one
		if !exists || finding.Commit > existingFinding.Commit {
			uniqueSecrets[key] = finding
		}
	}

	// Convert the map back to a slice
	var result []TrufflehogFinding
	for _, finding := range uniqueSecrets {
		result = append(result, finding)
	}

	return result
}

// determineSeverity assigns a severity level based on the detector name and raw content
func determineTrufflehogSeverity(detectorName string, raw string) string {
	// High severity detectors
	highSeverityDetectors := map[string]bool{
		"AWS":                    true,
		"Azure":                  true,
		"GitHub":                 true,
		"Gitlab":                 true,
		"SlackToken":             true,
		"GitHubApp":              true,
		"GoogleAPIKey":           true,
		"TwilioAPIKey":           true,
		"SendGridAPIToken":       true,
		"GoDaddy":                true,
		"CloudflareApiToken":     true,
		"CloudflareGlobalApiKey": true,
	}

	if highSeverityDetectors[detectorName] {
		return "high"
	}

	// Check raw content for keywords indicating high severity
	highSeverityKeywords := []string{"password", "token", "key", "secret", "credential", "auth"}
	lowercaseRaw := strings.ToLower(raw)
	for _, keyword := range highSeverityKeywords {
		if strings.Contains(lowercaseRaw, keyword) {
			return "high"
		}
	}

	return "medium" // Default severity
}
