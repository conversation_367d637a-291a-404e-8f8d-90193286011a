package core

import (
	"fmt"
	"net/url"

	tgbot "github.com/go-telegram-bot-api/telegram-bot-api/v5"
	"github.com/rs/zerolog/log"
)

// SendTelegram sends a notification message to a Telegram chat via bot
func SendTelegram(botID string, chatID int64, message Notification) error {
	// Validate input
	if botID == "" {
		return fmt.Errorf("empty Telegram bot ID")
	}

	// Redact secret for security if it's a scan notification
	redactedSecret := "*****"
	if message.Type == ScanNotification && len(message.Secret) > 7 {
		redactedSecret = message.Secret[:3] + "***" + message.Secret[len(message.Secret)-3:]
	}

	// Create message text based on notification type
	var messageText string
	var ghLink string

	switch message.Type {
	case ScanNotification:
		// Format for scan notifications
		if message.File != "" && message.Commit != "" {
			ghLink = fmt.Sprintf("%s/blob/%s/%s#L%d",
				message.RepoURL, message.Commit, url.PathEscape(message.File), message.Line)
		} else {
			ghLink = message.RepoURL
		}

		messageText = fmt.Sprintf(
			"🔐 *Security Alert*\n"+
				"🔔 *Octopi detected a potential secret*\n\n"+
				"*Rule:* `%s`\n"+
				"*Secret:* `%s`\n"+
				"*File:* `%s`\n"+
				"*Author:* %s\n"+
				"*Commit:* [%s](%s/commit/%s)\n"+
				"*Repository:* [%s](%s)\n"+
				"*Timestamp:* %s\n\n"+
				"%s"+
				"[🔍 View Code on GitHub](%s)",
			message.Rule,
			redactedSecret,
			message.File,
			message.Author,
			message.Commit[:7], message.RepoURL, message.Commit,
			message.RepoURL, message.RepoURL,
			message.Timestamp,
			func() string {
				if message.DanglerCommit {
					return "⛳ *Dangling Commit:* This secret was found in a dangling commit\n\n"
				}
				return ""
			}(),
			ghLink,
		)

	case WatchNotification:
		// Format for watch notifications
		ghLink = fmt.Sprintf("%s/commit/%s",
			message.RepoURL, message.Commit)

		messageText = fmt.Sprintf(
			"📡 *Commit Watch*\n"+
				"👀 *Octopi detected a watched commit*\n\n"+
				"*Author:* %s\n"+
				"*Commit:* [%s](%s)\n"+
				"*Repository:* [%s](%s)\n"+
				"*Timestamp:* %s\n\n"+
				"*Message:* _%s_\n\n"+
				"*Watch Filter:* `%s`\n\n"+
				"[🔍 View Commit on GitHub](%s)",
			message.Author,
			message.Commit[:7], ghLink,
			message.RepoURL, message.RepoURL,
			message.Timestamp,
			tgbot.EscapeText(tgbot.ModeMarkdown, message.CommitMsg),
			message.Query,
			ghLink,
		)
	}

	// Create bot instance with error handling
	bot, err := tgbot.NewBotAPI(botID)
	if err != nil {
		return err
	}

	// Generate and prepare message
	log.Debug().Msgf("Sending Telegram notification for rule: %s", message.Rule)

	// Configure message options
	msg := tgbot.NewMessage(chatID, messageText)
	msg.ParseMode = tgbot.ModeMarkdown
	msg.DisableWebPagePreview = true

	// Send message with proper error handling
	_, err = bot.Send(msg)
	if err != nil {
		return err
	}

	log.Debug().Msgf("Successfully sent Telegram notification for rule: %s", message.Rule)
	return nil
}
