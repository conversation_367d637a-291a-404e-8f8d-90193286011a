# Sensitive file patterns for monitoring
# Lines starting with # are comments and will be ignored

# Private keys and certificates
*.key
*.pem
*.p12
*.pfx
*.crt
*.cer
*.der

# SSH keys
id_rsa
id_dsa
id_ecdsa
id_ed25519
*.pub

# Configuration files that might contain secrets
config.yaml
config.yml
config.json
.env
.env.local
.env.production
.env.staging
settings.py
settings.json
application.properties
application.yml

# Database configuration
database.yml
database.json
db.conf

# Cloud provider credentials
.aws/credentials
.aws/config
gcp-key.json
azure-credentials.json

# Docker and Kubernetes secrets
docker-compose.yml
docker-compose.yaml
secrets.yaml
secrets.yml

# API keys and tokens
api-keys.txt
tokens.txt
credentials.txt

# Backup files that might contain sensitive data
*.sql
*.dump
*.backup

# Common secret file patterns
secret
secrets
password
passwords
credential
credentials
token
tokens
