package cmd

import (
	"bufio"
	"context"
	"fmt"
	"os"
	"os/signal"
	"strings"
	"syscall"
	"time"

	"github.com/fatih/color"
	"github.com/rs/zerolog/log"
	"github.com/spf13/cobra"
	"gitlab.com/hudjefa/octopi/core"
	"golang.org/x/text/cases"
	"golang.org/x/text/language"
)

var (
	// Octopi options
	updateRepos        bool // Whether to update local repository copies
	forceRescan        bool // Whether to force a full re-scan for already scanned repositories
	skipAdditionalRefs bool // Whether to skip fetching Git additional refs
	concurrency        int  // Number of threads/workers to use for scanning tools

	// Manual repository scanning options
	manualRepos     []string // List of repositories to scan manually (owner/repo format)
	manualReposFile string   // File containing list of repositories to scan manually
	manualOrgs      []string // List of organizations to scan all repositories from
	manualUsers     []string // List of users to scan all repositories from
	skipDatabase    bool     // Skip database repositories when manual targets are specified

	// Gitleaks options
	gitleaksEnabled            bool     // Whether to enable the Gitleaks tool
	gitleaksConfig             string   // Path to Gitleaks config file
	gitleaksEnabledRules       []string // List of Gitleaks rules to enable
	gitleaksMaxTargetMegaBytes string   // Maximum file size to scan. files larger than this will be skipped

	// Dangler options
	danglerEnabled         bool    // Whether to enable the Dangler object discovery
	danglerMaxChar         int     // Maximum SHA character length to test (4-6)
	danglerCollision       float64 // Collision threshold for SHA length calculation
	danglerChunkSize       int     // Number of commits to test per GraphQL query
	danglerMaxQueries      int     // Maximum number of GraphQL queries to make
	danglerCacheDir        string  // Cache directory for brute force results
	danglerTestCommits     string  // Test specific commits (comma-separated) for debugging
	scanOnlyDanglerCommits bool    // Whether to only scan dangling commits discovered by Dangler

	// Trufflehog options
	trufflehogEnabled             bool     // Whether to enable the Trufflehog tool
	trufflehogOnlyVerified        bool     // Whether to only output Trufflehog verified findings
	trufflehogNoVerification      bool     // Whether to skip verification of Trufflehog results
	trufflehogOnlyCustomVerifiers bool     // Whether to use only custom verification endpoints
	trufflehogFilterUnverified    bool     // Whether to output only first unverified result per chunk per detector
	trufflehogIncludeDetectors    []string // List of Trufflehog detector types to include
	trufflehogExcludeDetectors    []string // List of Trufflehog detector types to exclude
)

const (
	DefaultCacheTTL     = 24 * time.Hour
	DefaultMaxCacheSize = 100
)

var (
	Blue   = color.New(color.FgHiBlue, color.Bold).SprintFunc()
	Cyan   = color.New(color.FgCyan, color.Bold).SprintFunc()
	Green  = color.New(color.FgHiGreen, color.Bold).SprintFunc()
	Red    = color.New(color.FgHiRed, color.Bold).SprintFunc()
	Yellow = color.New(color.FgHiYellow, color.Bold).SprintFunc()

	boldYellowPrinter = color.New(color.Bold, color.FgYellow)
	yellowPrinter     = color.New(color.FgHiYellow)
	greenPrinter      = color.New(color.FgHiGreen)
	boldGreenPrinter  = color.New(color.Bold, color.FgHiGreen)
	whitePrinter      = color.New(color.FgWhite)
	boldWhitePrinter  = color.New(color.Bold, color.FgWhite)
)

const (
	helpTemplate  = `{{.Short}}{{printf "\n\n"}}` + usageTemplate
	usageTemplate = `Usage:
  {{if .Runnable}}{{.UseLine}}{{end}}

Flags:
      --gitleaks                  Scan repositories with Gitleaks.
      --trufflehog                Scan repositories with Trufflehog.
      --dangler                   Find dangling commits and create git refs for discovered commits.
      --skip-additional-refs      Skip fetching Git additional refss.
      --force-rescan              Force full re-scan for already scanned repositories.
      --update                    Scan new commits for already scanned repositories and update metadata.
      --concurrency               Number of threads/workers to use for scanning tools (0 = use tool defaults).
  -h, --help                      help for scan
  -v, --version                   Show application version.

Manual Repository Scanning:
      --repo                      Manually specify repositories to scan (format: owner/repo). Can be repeated.
      --repos-file                File containing list of repositories to scan (one per line, format: owner/repo).
      --org                       Scan all repositories from specified organizations. Can be repeated.
      --user                      Scan all repositories from specified users. Can be repeated.

Dangler Options:
      --dangler-max-char          Maximum SHA character length to test (4-6).
      --dangler-collision         Collision threshold for SHA length calculation.
      --dangler-chunk-size        Number of commits to test per GraphQL query.
      --dangler-max-queries       Maximum number of GraphQL queries to make.
      --dangler-cache             Cache directory for brute force results.
      --dangler-test-commits      Test specific commits (comma-separated) for debugging.
      --scan-only-dangler-commits Only scan dangling commits found by Dangler.

Gitleaks Options:
      --gitleaks-config           Path to Gitleaks config file.
      --enable-rule               Only enable specific rules by Rule ID.
      --max-target-megabytes      Maximum file size to scan. files larger than this will be skipped.

Trufflehog Options:
      --no-verification           Don't verify Trufflehog results.
      --verified-only             Only output Trufflehog verified findings.
      --filter-unverified         Output only first unverified result per chunk per detector.
      --custom-verifiers-only     Use only custom verification endpoints.
      --include-detectors         Comma separated list of detector types to include.
      --exclude-detectors         Comma separated list of detector types to exclude.

Global Flags:
  -d, --debug                    Run Octopi in debug mode.
  -c, --config                   Path to octopi config file (default is ~/.octopi/config.yaml)
      --slack                    Send verified findings to Slack via Slack webhook.
      --telegram                 Send verified findings to Telegram via Telegram bot.
  -O, --output-dir               Path to directory for saving JSON format scan results.
  -f, --include-forks            Include repository forks in recon, search and scan.
  -m, --max-size                 Maximum repository size in MB for recon, search and scan, unlimited by default.


Copyright © 2024 Hologram <<EMAIL>>
Distributed under the Apache License Version 2.0 (Apache-2.0)
For further details, visit https://github.com/hologram/octopi
`
)

var scanCmd = &cobra.Command{
	Use:   "scan",
	Short: "Scan Github repositories saved in database for secrets",
	Run: func(cmd *cobra.Command, args []string) {
		runScan(cmd)
	},
}

// readReposFromFile reads repository names from a file (one per line)
func readReposFromFile(filename string) ([]string, error) {
	if filename == "" {
		return nil, nil
	}

	file, err := os.Open(filename)
	if err != nil {
		return nil, fmt.Errorf("failed to open repos file %s: %w", filename, err)
	}
	defer file.Close()

	var repos []string
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line != "" && !strings.HasPrefix(line, "#") { // Skip empty lines and comments
			repos = append(repos, line)
		}
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("error reading repos file %s: %w", filename, err)
	}

	return repos, nil
}

// fetchReposFromOrgs fetches all repositories from specified organizations
func fetchReposFromOrgs(ctx context.Context, ghClient *core.Github, orgs []string) ([]core.Octopi, error) {
	var allRepos []core.Octopi

	for _, org := range orgs {
		log.Info().Msgf("Fetching repositories from organization: %s", org)

		repos, err := core.GetReposByOrg(ctx, ghClient.Client, org)
		if err != nil {
			log.Error().Msgf("Failed to fetch repositories from organization %s: %s", org, err.Error())
			continue
		}

		for _, repo := range repos {
			octopi := core.Octopi{
				RepoName: repo.GetName(),
				RepoURL:  repo.GetHTMLURL(),
				RepoSize: repo.GetSize(),
				Owner:    repo.GetOwner().GetLogin(),
				Query:    fmt.Sprintf("org:%s", org),
			}
			allRepos = append(allRepos, octopi)
		}

		log.Info().Msgf("Found %d repositories in organization %s", len(repos), org)
	}

	return allRepos, nil
}

// fetchReposFromUsers fetches all repositories from specified users
func fetchReposFromUsers(ctx context.Context, ghClient *core.Github, users []string) ([]core.Octopi, error) {
	var allRepos []core.Octopi

	for _, user := range users {
		log.Info().Msgf("Fetching repositories from user: %s", user)

		repos, err := core.GetUserRepos(ctx, ghClient.Client, user)
		if err != nil {
			log.Error().Msgf("Failed to fetch repositories from user %s: %s", user, err.Error())
			continue
		}

		for _, repo := range repos {
			octopi := core.Octopi{
				RepoName: repo.GetName(),
				RepoURL:  repo.GetHTMLURL(),
				RepoSize: repo.GetSize(),
				Owner:    repo.GetOwner().GetLogin(),
				Query:    fmt.Sprintf("user:%s", user),
			}
			allRepos = append(allRepos, octopi)
		}

		log.Info().Msgf("Found %d repositories for user %s", len(repos), user)
	}

	return allRepos, nil
}

// createManualOctopi creates Octopi structs from manual repository specifications
func createManualOctopi(repos []string) ([]core.Octopi, error) {
	var octopis []core.Octopi

	for _, repo := range repos {
		owner, repoName, err := core.ParseGithubRepo(repo)
		if err != nil {
			log.Error().Msgf("Invalid repository format %s: %s", repo, err.Error())
			continue
		}

		octopi := core.Octopi{
			RepoName: repoName,
			RepoURL:  fmt.Sprintf("https://github.com/%s/%s", owner, repoName),
			Owner:    owner,
			Query:    fmt.Sprintf("manual:%s", repo),
		}
		octopis = append(octopis, octopi)
	}

	return octopis, nil
}

func init() {
	// Octopi options
	scanCmd.Flags().BoolVar(&forceRescan, "force-rescan", false, "Force full re-scan for already scanned repositories.")
	scanCmd.Flags().BoolVar(&updateRepos, "update", false, "Scan new commits for already scanned repositories and update metadata.")
	scanCmd.Flags().IntVar(&concurrency, "concurrency", 0, "Number of threads/workers to use for scanning tools (0 = use tool defaults).")
	scanCmd.Flags().BoolVar(&skipAdditionalRefs, "skip-additional-refs", false, "Skip fetching Git additional refs.")

	// Manual repository scanning flags
	scanCmd.Flags().StringSliceVar(&manualRepos, "repo", nil, "Manually specify repositories to scan (format: owner/repo). Can be repeated.")
	scanCmd.Flags().StringVar(&manualReposFile, "repos-file", "", "File containing list of repositories to scan (one per line, format: owner/repo)")
	scanCmd.Flags().StringSliceVar(&manualOrgs, "org", nil, "Scan all repositories from specified organizations. Can be repeated.")
	scanCmd.Flags().StringSliceVar(&manualUsers, "user", nil, "Scan all repositories from specified users. Can be repeated.")

	// Gitleaks options
	scanCmd.Flags().BoolVar(&gitleaksEnabled, "gitleaks", false, "Scan repositories with Gitleaks.")
	scanCmd.Flags().StringVar(&gitleaksConfig, "gitleaks-config", "", "Path to Gitleaks config file.")
	scanCmd.Flags().StringVar(&gitleaksMaxTargetMegaBytes, "max-target-megabytes", "0", "Maximum file size to scan. files larger than this will be skipped.")
	scanCmd.Flags().StringSliceVar(&gitleaksEnabledRules, "enable-rules", nil, "Comma separated list of Gitleaks rules to enable.")

	// Trufflehog options
	scanCmd.Flags().BoolVar(&trufflehogEnabled, "trufflehog", false, "Scan repositories with Trufflehog.")
	scanCmd.Flags().BoolVar(&trufflehogNoVerification, "no-verification", false, "Don't verify Trufflehog results.")
	scanCmd.Flags().BoolVar(&trufflehogOnlyVerified, "verified-only", false, "Only output Trufflehog verified findings.")
	scanCmd.Flags().BoolVar(&trufflehogFilterUnverified, "filter-unverified", false, "Output only first unverified result per chunk per detector.")
	scanCmd.Flags().BoolVar(&trufflehogOnlyCustomVerifiers, "custom-verifiers-only", false, "Use only custom verification endpoints.")
	scanCmd.Flags().StringSliceVar(&trufflehogIncludeDetectors, "include-detectors", nil, "Comma separated list of detector types to include.")
	scanCmd.Flags().StringSliceVar(&trufflehogExcludeDetectors, "exclude-detectors", nil, "Comma separated list of detector types to exclude.")

	// Dangler options
	scanCmd.Flags().BoolVar(&danglerEnabled, "dangler", false, "Enable Dangler object discovery")
	scanCmd.Flags().IntVar(&danglerMaxChar, "dangler-max-char", 5, "Maximum SHA character length to test (4-6)")
	scanCmd.Flags().Float64Var(&danglerCollision, "dangler-collision", 1.0, "Collision threshold for SHA length calculation")
	scanCmd.Flags().IntVar(&danglerChunkSize, "dangler-chunk-size", 350, "Number of commits to test per GraphQL query")
	scanCmd.Flags().IntVar(&danglerMaxQueries, "dangler-max-queries", 100, "Maximum number of GraphQL queries to make")
	scanCmd.Flags().StringVar(&danglerCacheDir, "dangler-cache", "", "Cache directory for brute force results")
	scanCmd.Flags().StringVar(&danglerTestCommits, "dangler-test-commits", "", "Test specific commits (comma-separated) for debugging")
	scanCmd.Flags().BoolVar(&scanOnlyDanglerCommits, "scan-only-dangler-commits", false, "Only scan dangling commits found by Dangler")

	scanCmd.MarkFlagFilename("gitleaks-config", "toml")
	scanCmd.MarkFlagFilename("trufflehog-config", "yaml")
	scanCmd.MarkFlagsOneRequired("gitleaks", "trufflehog")
	scanCmd.MarkFlagsMutuallyExclusive("verified-only", "no-verification")
	scanCmd.MarkFlagsMutuallyExclusive("dangler", "dangler")

	scanCmd.Flags().SortFlags = false
	scanCmd.PersistentFlags().SortFlags = false
	scanCmd.InheritedFlags().SortFlags = false
	scanCmd.SetHelpTemplate(helpTemplate)
	scanCmd.SetUsageTemplate(usageTemplate)
}

func runScan(cmd *cobra.Command) {
	start := time.Now()
	config, _ := cmd.Flags().GetString("config")

	if debug {
		log.Info().Msgf("Running in Debug Mode")
	}

	// Check for required executables
	if !core.CommandExists(GitBinary) {
		log.Fatal().Msgf("executable %s not found in PATH", GitBinary)
	}
	if !core.CommandExists(GitleaksBinary) {
		log.Fatal().Msgf("executable %s not found in PATH", GitleaksBinary)
	}
	if !core.CommandExists(TrufflehogBinary) {
		log.Fatal().Msgf("executable %s not found in PATH", TrufflehogBinary)
	}

	cfg := core.InitConfig(config)
	ctx, cancel := context.WithCancel(context.Background())

	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)
	defer func() {
		signal.Stop(c)
		cancel()
		core.EnsureCleanup(core.TempDirPath())
	}()
	go func() {
		select {
		case <-c:
			cancel()
			core.EnsureCleanup(core.TempDirPath())
			os.Exit(1)
		case <-ctx.Done():
			return
		}
	}()

	ghClient, ghGQLClient, err := core.CreateGithubClient(ctx, cfg.GithubPAT)
	if err != nil {
		log.Error().Msgf("%s", err.Error())
	}

	if gitleaksConfig == "" {
		gitleaksConfig = cfg.GitLeaksConfig
	}

	// Create cache manager
	var cache *core.CacheManager
	cache = core.NewCacheManager(DefaultMaxCacheSize, DefaultCacheTTL)

	scan := &core.Scan{
		SendSlack:    sendSlack,
		SendTelegram: sendTelegram,
		SendDiscord:  sendDiscord,
		UpdateRepos:  updateRepos,
		ForceRescan:  forceRescan,
		Concurrency:  concurrency,
		Github: core.Github{
			Cache:         cache,
			Client:        ghClient,
			GraphQLClient: ghGQLClient,
		},
		Dangler: core.Dangler{
			Enabled:            danglerEnabled,
			MaxCharLength:      danglerMaxChar,
			CollisionThreshold: danglerCollision,
			ChunkSize:          danglerChunkSize,
			MaxQueries:         danglerMaxQueries,
			GithubToken:        cfg.GithubPAT,
			CacheDir:           danglerCacheDir,
			TestCommits:        danglerTestCommits,
		},
		Gitleaks: core.Gitleaks{
			Enabled:            gitleaksEnabled,
			Config:             gitleaksConfig,
			MaxTargetMegaBytes: gitleaksMaxTargetMegaBytes,
			EnabledRules:       gitleaksEnabledRules,
			SkipDuplicates:     true,
		},
		Trufflehog: core.Trufflehog{
			Enabled:             trufflehogEnabled,
			OnlyVerified:        trufflehogOnlyVerified,
			NoVerification:      trufflehogNoVerification,
			FilterUnverified:    trufflehogFilterUnverified,
			OnlyCustomVerifiers: trufflehogOnlyCustomVerifiers,
			IncludeDetectors:    trufflehogIncludeDetectors,
			ExcludeDetectors:    trufflehogExcludeDetectors,
			SkipDuplicates:      true,
		},
	}

	core.InitOctopiDB()
	var octosToScan []core.Octopi

	// Initialize report files
	danglerReport := core.GetReportFile(core.DanglerScanner)
	gitleaksReport := core.GetReportFile(core.GitleaksScanner)
	trufflehogReport := core.GetReportFile(core.TrufflehogScanner)
	sensitiveLeaksReport := core.GetReportFile(core.GitleaksSensitive)
	verifiedLeaksReport := core.GetReportFile(core.TrufflehogVerified)

	// Check if manual targets are specified
	hasManualTargets := len(manualRepos) > 0 || manualReposFile != "" || len(manualOrgs) > 0 || len(manualUsers) > 0

	// Validate the configuration
	if err := validateScanConfig(cfg, scan, hasManualTargets); err != nil {
		log.Fatal().Msgf("Configuration error: %s", err.Error())
	}

	if hasManualTargets {
		log.Info().Msg("Manual repository targets specified, building target list...")
		skipDatabase = true
		// Load repositories from file if specified
		if manualReposFile != "" {
			fileRepos, err := readReposFromFile(manualReposFile)
			if err != nil {
				log.Fatal().Msgf("Failed to read repos file: %s", err.Error())
			}
			manualRepos = append(manualRepos, fileRepos...)
		}

		// Create Octopi structs from manual repository specifications
		if len(manualRepos) > 0 {
			manualOctopis, err := createManualOctopi(manualRepos)
			if err != nil {
				log.Error().Msgf("Error processing manual repositories: %s", err.Error())
			} else {
				octosToScan = append(octosToScan, manualOctopis...)
				log.Info().Msgf("Added %d manual repositories to scan list", len(manualOctopis))
			}
		}

		// Fetch repositories from organizations
		if len(manualOrgs) > 0 {
			orgRepos, err := fetchReposFromOrgs(ctx, &scan.Github, manualOrgs)
			if err != nil {
				log.Error().Msgf("Error fetching organization repositories: %s", err.Error())
			} else {
				octosToScan = append(octosToScan, orgRepos...)
				log.Info().Msgf("Added %d organization repositories to scan list", len(orgRepos))
			}
		}

		// Fetch repositories from users
		if len(manualUsers) > 0 {
			userRepos, err := fetchReposFromUsers(ctx, &scan.Github, manualUsers)
			if err != nil {
				log.Error().Msgf("Error fetching user repositories: %s", err.Error())
			} else {
				octosToScan = append(octosToScan, userRepos...)
				log.Info().Msgf("Added %d user repositories to scan list", len(userRepos))
			}
		}
	}

	// Load repositories from database unless manual targets are specified (unless explicitly requested)
	if !skipDatabase {
		var dbRepos []core.Octopi

		// Optimize repository selection logic
		if scan.UpdateRepos || scan.ForceRescan {
			// When updating or forcing rescan, get all repositories
			// If forcing rescan, include failed repositories; otherwise, skip them
			dbRepos, err = core.GetOctos(false, !scan.ForceRescan)
		} else {
			// When doing a normal scan, get only unscanned repositories and skip failed ones
			dbRepos, err = core.GetOctos(true, true)
		}

		if err != nil {
			log.Error().Msgf("Error loading repositories from database: %s", err.Error())
		} else {
			octosToScan = append(octosToScan, dbRepos...)
			log.Info().Msgf("Added %d repositories from database to scan list", len(dbRepos))
		}
	} else {
		if hasManualTargets {
			log.Info().Msg("Skipping database repositories (manual targets specified)")
		} else {
			log.Info().Msg("No repositories found in database")
		}
	}

	if len(octosToScan) == 0 {
		log.Warn().Msgf("Found no repositories to scan")
		return
	}

	log.Info().Msgf("Total repositories to process: %d", len(octosToScan))

	// Pre-filter repositories that should be skipped
	var filteredOctos []core.Octopi
	for _, octo := range octosToScan {
		// Skip ignored users early
		if core.IsUserIgnored(cfg, octo.Owner) {
			log.Debug().Msgf("Skipping ignored user: %s", octo.Owner)
			continue
		}

		// Skip ignored repos early
		if core.IsRepoIgnored(cfg, octo.Owner, octo.RepoName) {
			log.Debug().Msgf("Skipping ignored repo: %s/%s", octo.Owner, octo.RepoName)
			continue
		}

		// Skip already scanned repos if not updating or forcing rescan
		if !updateRepos && !forceRescan {
			// Define these variables here so they're in scope
			glksVirgin := octo.ScanStatus.Gitleaks.Date.IsZero()
			thogVirgin := octo.ScanStatus.Trufflehog.Date.IsZero()

			// Skip if both scanners have already run
			if !glksVirgin && !thogVirgin {
				log.Debug().Msgf("Skipping %s/%s: already scanned by both scanners", octo.Owner, octo.RepoName)
				continue
			}

			// Check if repositories have failed scanning
			glksFailed := octo.ScanStatus.Gitleaks.Commit == "SCAN_FAILED"
			thogFailed := octo.ScanStatus.Trufflehog.Commit == "SCAN_FAILED"

			// Skip if only Gitleaks is enabled but repo already scanned by Gitleaks
			if scan.Gitleaks.Enabled && !scan.Trufflehog.Enabled && !glksVirgin {
				log.Debug().Msgf("Skipping %s/%s: already scanned by Gitleaks", octo.Owner, octo.RepoName)
				continue
			}

			// Skip if only Trufflehog is enabled but repo already scanned by Trufflehog
			if !scan.Gitleaks.Enabled && scan.Trufflehog.Enabled && !thogVirgin {
				log.Debug().Msgf("Skipping %s/%s: already scanned by Trufflehog", octo.Owner, octo.RepoName)
				continue
			}

			// Skip if only Gitleaks is enabled but repo already scanned by Gitleaks or failed
			if scan.Gitleaks.Enabled && !scan.Trufflehog.Enabled && (!glksVirgin || glksFailed) {
				if glksFailed {
					log.Debug().Msgf("Skipping %s/%s: previous Gitleaks scan failed (use --force-rescan to retry)", octo.Owner, octo.RepoName)
				} else {
					log.Debug().Msgf("Skipping %s/%s: already scanned by Gitleaks", octo.Owner, octo.RepoName)
				}
				continue
			}

			// Skip if only Trufflehog is enabled but repo already scanned by Trufflehog or failed
			if !scan.Gitleaks.Enabled && scan.Trufflehog.Enabled && (!thogVirgin || thogFailed) {
				if thogFailed {
					log.Debug().Msgf("Skipping %s/%s: previous Trufflehog scan failed (use --force-rescan to retry)", octo.Owner, octo.RepoName)
				} else {
					log.Debug().Msgf("Skipping %s/%s: already scanned by Trufflehog", octo.Owner, octo.RepoName)
				}
				continue
			}
		}

		filteredOctos = append(filteredOctos, octo)
	}

	log.Info().Msgf("Processing %d repositories after filtering", len(filteredOctos))

	// Process only the filtered repositories
	for _, octo := range filteredOctos {
		log.Info().Msgf("Processing Github Octo: %s/%s", octo.Owner, octo.RepoName)

		// Normalize the repository URL to ensure consistent handling
		normalizedURL, err := core.NormalizeGitHubURL(octo.RepoURL)
		if err != nil {
			log.Error().Msgf("Invalid repository URL format for %s/%s: %s", octo.Owner, octo.RepoName, err.Error())
			continue
		}
		// Update the repository URL in the database if it changed
		if normalizedURL != octo.RepoURL && !skipDatabase {
			log.Debug().Msgf("Normalizing repository URL from %s to %s", octo.RepoURL, normalizedURL)
			octo.RepoURL = normalizedURL
			// Update the database with the normalized URL
			if err := core.UpdateOctoURL(octo.ID, normalizedURL); err != nil {
				log.Error().Msgf("Failed to update repository URL in database: %s", err.Error())
			}
		}

		repoGitleaksScanDone := false
		repoTrufflehogScanDone := false

		// Make sure skipAdditionalRefs is false if we're scanning dangling commits
		if scan.Dangler.Enabled {
			skipAdditionalRefs = false
		}

		clonePath, gitRepo, err := core.CloneRepoUsingUnauthenticated(ctx, octo.RepoURL, skipAdditionalRefs)

		// Ensure cleanup happens no matter what
		defer func(path string) {
			if path != "" {
				core.EnsureCleanup(path)
			}
		}(clonePath)

		if err != nil {
			core.CleanOnError(&err, clonePath)
			log.Error().Msgf("Failed to clone repo %s/%s: %s", octo.Owner, octo.RepoName, err.Error())
			// Mark repository as failed for both scanners if they're enabled
			if !skipDatabase {
				if scan.Gitleaks.Enabled {
					_ = core.UpdateOctoScanFailure(octo.RepoURL, core.GitleaksScanner, fmt.Sprintf("Clone failed: %s", err.Error()))
				}
				if scan.Trufflehog.Enabled {
					_ = core.UpdateOctoScanFailure(octo.RepoURL, core.TrufflehogScanner, fmt.Sprintf("Clone failed: %s", err.Error()))
				}
			}

			continue
		}

		latestCommit, err := core.GetLatestGitCommit(clonePath, gitRepo)
		if err != nil {
			core.CleanOnError(&err, clonePath)
			log.Error().Msgf("Unable to get latest commit: %s", err.Error())
			// Mark repository as failed for both scanners if they're enabled
			if !skipDatabase {
				if scan.Gitleaks.Enabled {
					_ = core.UpdateOctoScanFailure(octo.RepoURL, core.GitleaksScanner, fmt.Sprintf("Failed to get latest commit: %s", err.Error()))
				}
				if scan.Trufflehog.Enabled {
					_ = core.UpdateOctoScanFailure(octo.RepoURL, core.TrufflehogScanner, fmt.Sprintf("Failed to get latest commit: %s", err.Error()))
				}
			}
			continue
		}
		if !skipDatabase {
			core.UpdateOcto(octo.RepoURL, latestCommit)
		}

		// Add Octo instance for scanners
		scan.Octopi = octo

		// Define these variables again for this scope
		glksVirgin := octo.ScanStatus.Gitleaks.Date.IsZero()
		thogVirgin := octo.ScanStatus.Trufflehog.Date.IsZero()

		// Optimize the dangling commits processing
		var danglingCommits []string
		danglingCommitsMap := make(map[string]bool)

		if scan.Dangler.Enabled {
			if !updateRepos && !forceRescan && !glksVirgin && !thogVirgin {
				log.Debug().Msgf("Skipping Octo Dangler scan as it's already scanned by Gitleaks and Trufflehog")
				continue
			}
			log.Info().Msg("Finding dangling commits with Dangler object discovery")
			danglingCommits, err = scan.RunDanglerDiscovery(ctx, clonePath)
			if err != nil {
				log.Warn().Msgf("Dangler discovery error: %s", err.Error())
			} else if len(danglingCommits) > 0 {
				log.Info().Msgf("Dangler discovery found %d dangling/hidden commits", len(danglingCommits))
				log.Info().Msgf("Fetching dangling commits into local branches for scanning")

				// Save current branch before fetching dangling commits
				originalBranch, err := core.GetCurrentBranch(clonePath)
				if err != nil {
					log.Warn().Msgf("Failed to get current branch: %v", err)
				}

				for _, c := range danglingCommits {
					if err = core.FetchDanglingCommit(clonePath, c); err != nil {
						log.Error().Msgf("Error while fetching dangling commit: %s", err)
						continue
					}
					// Add commit to map for faster lookups
					danglingCommitsMap[c] = true
					dgFinding := &core.DanglerReport{
						RepoURL: octo.RepoURL,
						Commit:  c,
						Tree:    fmt.Sprintf("%s/tree/%s", octo.RepoURL, c),
					}

					if err := core.WriteJSONReport(dgFinding, danglerReport); err != nil {
						log.Error().Msgf("Failed to save Dangler scan results: %s", err.Error())
					}
				}

				// Restore original branch after fetching all dangling commits
				if originalBranch != "" {
					if err := core.CheckoutBranch(clonePath, originalBranch); err != nil {
						log.Warn().Msgf("Failed to restore original branch %s: %v", originalBranch, err)
					} else {
						log.Debug().Msgf("Restored original branch: %s", originalBranch)
					}
				}
			} else {
				log.Info().Msgf("No dangling commits found by Dangler object discovery for repository: %s/%s", octo.Owner, octo.RepoName)
			}
		}

		if scan.Dangler.Enabled && scanOnlyDanglerCommits {
			if len(danglingCommits) > 0 {
				log.Info().Msgf("Configuring scanners to only check %d dangling commits", len(danglingCommits))
				// Only scan the dangling commits we found
				scan.Gitleaks.SpecificCommits = danglingCommits
				scan.Trufflehog.SpecificCommits = danglingCommits
			} else {
				log.Info().Msg("No dangling commits found, but scanOnlyDanglerCommits is enabled. Skipping scans.")
				continue // Skip to the next repository
			}
		}

		if scan.Gitleaks.Enabled {
			if !updateRepos && !forceRescan && !glksVirgin {
				log.Debug().Msgf("Octo already scanned by Gitleaks. Skipping.")
				continue
			}
			log.Info().Msgf("Starting Gitleaks scan for repository: %s/%s", octo.Owner, octo.RepoName)

			findings, err := scan.RunGitleaks(ctx, octo, clonePath)
			if err != nil {
				log.Error().Msgf("Gitleaks scan failed for repository: %s/%s: %s", octo.Owner, octo.RepoName, err.Error())
				if !skipDatabase {
					_ = core.UpdateOctoScanFailure(octo.RepoURL, core.GitleaksScanner, fmt.Sprintf("Scan failed: %s", err.Error()))
				}
				continue
			}

			repoGitleaksScanDone = true

			for _, finding := range findings {
				finding.RepoURL = octo.RepoURL
				finding.DanglerCommit = danglingCommitsMap[finding.Commit]
				if finding.Severity == "high" {
					err := core.WriteJSONReport(finding, sensitiveLeaksReport)
					if err != nil {
						log.Error().Msgf("Failed to save Gitleaks sensitive scan results: %s", err.Error())
					}
				} else {
					err := core.WriteJSONReport(finding, gitleaksReport)
					if err != nil {
						log.Error().Msgf("Failed to save Gitleaks scan results: %s", err.Error())
					}
				}

				printer := whitePrinter
				boldWhitePrinter.Print("\n✨ Found GitLeaks secret 🐷🔑\n")
				printer.Printf("RuleID: %s\n", finding.RuleID)
				printer.Printf("Description: %s\n", finding.Description)
				printer.Printf("Secret: %s\n", Red(finding.Match))
				printer.Printf("Entropy: %s\n", Red(finding.Entropy))
				printer.Printf("Commit: %s\n", finding.Commit)
				if finding.DanglerCommit {
					printer.Printf("Dangler: ⛳ Dangling Commit Secret ⛳\n")
				}
				printer.Printf("Author: %s (%s)\n", finding.Email, finding.Author)
				printer.Printf("Github Link: %s\n", finding.Link)
				printer.Printf("Github Query: %s\n", octo.Query)
				printer.Printf("Repository: %s\n", finding.RepoURL)
				printer.Printf("Timestamp: %s\n", finding.Date)
			}
			fmt.Printf("\n")
		}

		if scan.Trufflehog.Enabled {
			if !updateRepos && !forceRescan && !thogVirgin {
				log.Debug().Msgf("Octo already scanned by Trufflehog. Skipping.")
				continue
			}
			log.Info().Msgf("Starting Trufflehog scan for repository: %s/%s", octo.Owner, octo.RepoName)
			findings, err := scan.RunTrufflehog(ctx, octo, clonePath)
			if err != nil {
				log.Error().Msgf("Trufflehog scan failed for repository: %s/%s: %s", octo.Owner, octo.RepoName, err.Error())
				if !skipDatabase {
					_ = core.UpdateOctoScanFailure(octo.RepoURL, core.TrufflehogScanner, fmt.Sprintf("Scan failed: %s", err.Error()))
				}
				continue
			}

			repoTrufflehogScanDone = true

			for _, finding := range findings {
				finding.RepoURL = octo.RepoURL
				finding.DanglerCommit = danglingCommitsMap[finding.Commit]

				printer := greenPrinter
				if finding.Verified {
					boldGreenPrinter.Print("\n⭕ Found Trufflehog verified secret 🐷🔑\n")
					if err := core.WriteJSONReport(finding, verifiedLeaksReport); err != nil {
						log.Error().Msgf("Failed to save Trufflehog verified scan results: %s", err.Error())
					}
					if sendSlack || sendTelegram || sendDiscord {
						message := &core.Notification{
							Type:          core.ScanNotification,
							Query:         octo.Query,
							Rule:          finding.DetectorName,
							Secret:        finding.Raw,
							Commit:        finding.Commit,
							Author:        finding.Email,
							File:          finding.File,
							Line:          finding.Line,
							Timestamp:     finding.Timestamp,
							RepoURL:       finding.RepoURL,
							DanglerCommit: finding.DanglerCommit,
						}
						if sendSlack {
							if err := core.SendSlack(cfg.SlackWebhook, *message); err != nil {
								log.Error().Msgf("Failed to send Slack notification: %s", err)
							}
						}
						if sendTelegram {
							if err := core.SendTelegram(cfg.TelegramBotId, cfg.TelegramChatId, *message); err != nil {
								log.Error().Msgf("Failed to send Telegram notification: %s", err)
							}
						}
						if sendDiscord {
							if err := core.SendDiscord(cfg.DiscordWebhook, *message); err != nil {
								log.Error().Msgf("Failed to send Discord notification: %s", err)
							}
						}
					}
				} else {
					printer = whitePrinter
					boldWhitePrinter.Print("\n✨ Found Trufflehog unverified secret 🐷🔑❓\n")
					if err := core.WriteJSONReport(finding, trufflehogReport); err != nil {
						log.Error().Msgf("Failed to save Trufflehog scan results: %s", err.Error())
					}
				}
				printer = whitePrinter
				printer.Printf("Detector: %s\n", finding.DetectorName)
				printer.Printf("Decoder: %s\n", finding.DecoderName)
				printer.Printf("Secret: %s\n", Red(finding.Raw))
				for k, v := range finding.ExtraData {
					printer.Printf(
						"%s: %v\n",
						cases.Title(language.AmericanEnglish).String(k),
						v)
				}
				printer.Printf("Commit: %s\n", finding.Commit)
				if finding.DanglerCommit {
					printer.Printf("Dangler: ⛳ Dangling Commit Secret ⛳\n")
				}
				if finding.VerificationError != "" {
					yellowPrinter.Printf("Verification issue: %s\n", finding.VerificationError)
				}
				printer.Printf("Author: %s \n", finding.Email)
				printer.Printf("Github Link: %s\n", finding.Link)
				printer.Printf("Github query: %s\n", octo.Query)
				printer.Printf("Repository: %s\n", octo.RepoURL)
				printer.Printf("Timestamp: %s\n", finding.Timestamp)
			}
			fmt.Printf("\n")
		}

		// Update Repository scan status and metadata
		if !skipDatabase && (repoGitleaksScanDone || repoTrufflehogScanDone) {
			if repoGitleaksScanDone {
				core.UpdateOctoScan(octo.RepoURL, core.GitleaksScanner, latestCommit)
			}
			if repoTrufflehogScanDone {
				core.UpdateOctoScan(octo.RepoURL, core.TrufflehogScanner, latestCommit)
			}
		}
		if err = os.RemoveAll(clonePath); err != nil {
			log.Error().Msgf("Unable to delete cloned Git repo: %s", clonePath)
		}
	}

	if err = os.RemoveAll(core.TempDirPath()); err != nil {
		log.Error().Msgf("Unable to delete octopi temp dir")
	}
	log.Info().Msgf("Octopi scan completed in %s", FormatDuration(time.Since(start)))
}

// validateScanConfig checks the configuration for scanning and returns an error if any required settings are missing or invalid.
func validateScanConfig(cfg *core.Config, scan *core.Scan, hasManualTargets bool) error {
	// Validate GitHub token if needed
	if scan.Github.Client == nil || scan.Github.GraphQLClient == nil {
		return fmt.Errorf("GitHub API clients not initialized properly. Check your GitHub token")
	}

	// Validate Dangler settings
	if scan.Dangler.Enabled {
		// Dangler can only be used with manual targets (--repos, --org, --user, --repos-file)
		if !hasManualTargets {
			return fmt.Errorf("Dangler can only be used with manual targets (--repos, --org, --user, or --repos-file)")
		}

		// Validate Dangler parameters
		if scan.Dangler.MaxCharLength < 4 || scan.Dangler.MaxCharLength > 6 {
			return fmt.Errorf("Dangler max character length must be between 4 and 6, got %d", scan.Dangler.MaxCharLength)
		}

		if scan.Dangler.ChunkSize <= 0 || scan.Dangler.ChunkSize > 900 {
			return fmt.Errorf("Dangler chunk size must be between 1 and 900, got %d", scan.Dangler.ChunkSize)
		}

		if scan.Dangler.MaxQueries <= 0 {
			return fmt.Errorf("Dangler max queries must be greater than 0, got %d", scan.Dangler.MaxQueries)
		}
	}

	// Validate Gitleaks configuration if enabled
	if scan.Gitleaks.Enabled {
		if scan.Gitleaks.Config != "" {
			if !core.FileExistsAndNotEmpty(scan.Gitleaks.Config) {
				return fmt.Errorf("Gitleaks config file not found or empty: %s", scan.Gitleaks.Config)
			}
		}
	}

	// Validate Trufflehog configuration if enabled
	if scan.Trufflehog.Enabled {
		// Check for mutually exclusive options
		if scan.Trufflehog.OnlyVerified && scan.Trufflehog.NoVerification {
			return fmt.Errorf("Conflicting Trufflehog options: cannot use both --verified-only and --no-verification")
		}
	}

	// Validate notification settings
	if scan.SendSlack && cfg.SlackWebhook == "" {
		return fmt.Errorf("Slack webhook URL not configured in config file")
	}

	if sendTelegram && (cfg.TelegramBotId == "" || cfg.TelegramChatId == 0) {
		return fmt.Errorf("Telegram bot ID or chat ID not configured in config file")
	}

	if sendDiscord && cfg.DiscordWebhook == "" {
		return fmt.Errorf("Discord webhook URL not configured in config file")
	}

	// Validate output directory if specified
	if outputDir != "" {
		// Expand tilde in output directory path
		expandedOutputDir, err := core.ExpandPath(outputDir)
		if err != nil {
			return fmt.Errorf("Failed to expand output directory path %s: %v", outputDir, err)
		}
		outputDir = expandedOutputDir // Update the global variable

		stat, err := os.Stat(outputDir)
		if err != nil {
			if os.IsNotExist(err) {
				// Try to create the directory
				if err := os.MkdirAll(outputDir, 0755); err != nil {
					return fmt.Errorf("Failed to create output directory %s: %v", outputDir, err)
				}
			} else {
				return fmt.Errorf("Error accessing output directory %s: %v", outputDir, err)
			}
		} else if !stat.IsDir() {
			return fmt.Errorf("Output path %s exists but is not a directory", outputDir)
		}
	}

	return nil
}
