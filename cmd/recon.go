package cmd

import (
	"context"
	"time"

	"github.com/rs/zerolog/log"
	"github.com/spf13/cobra"
	"gitlab.com/hudjefa/octopi/core"
)

var (
	orgs             []string
	repos            []string
	users            []string
	emails           []string
	orgsFile         string
	reposFile        string
	usersFile        string
	emailsFile       string
	minContributions int
	findContributors bool
	findLeakedEmails bool
	outputFormat     string
)

var reconSource = (*core.Github)(nil)

// filterByMinContributions filters reports based on minimum contributions
func filterByMinContributions(reports []core.ReconReport, minContribs int) []core.ReconReport {
	if minContribs <= 0 {
		return reports
	}

	var filtered []core.ReconReport
	for _, report := range reports {
		if report.Contributions >= minContribs {
			filtered = append(filtered, report)
		}
	}
	return filtered
}

var reconCmd = &cobra.Command{
	Use:   "recon",
	Short: "Recon Github organizations members, repository contributors and emails to find assets to scan",
	Run: func(cmd *cobra.Command, args []string) {
		runRecon(cmd)
	},
}

func init() {
	// Target specification flags
	reconCmd.Flags().StringSliceVarP(&orgs, "org", "o", nil, "Comma separated list of Github organization(s) to recon. You can repeat this flag.")
	reconCmd.Flags().StringSliceVarP(&repos, "repo", "r", nil, "Comma separated list of Github repo(s) to enumerate. You can repeat this flag.")
	reconCmd.Flags().StringSliceVarP(&users, "user", "u", nil, "Comma separated list of Github user(s) to enumerate. You can repeat this flag.")
	reconCmd.Flags().StringSliceVarP(&emails, "email", "e", nil, "Comma separated list of email(s) to enumerate. You can repeat this flag.")

	// File-based input flags
	reconCmd.Flags().StringVar(&orgsFile, "orgs-file", "", "File containing list of organizations (one per line)")
	reconCmd.Flags().StringVar(&reposFile, "repos-file", "", "File containing list of repositories (one per line)")
	reconCmd.Flags().StringVar(&usersFile, "users-file", "", "File containing list of users (one per line)")
	reconCmd.Flags().StringVar(&emailsFile, "emails-file", "", "File containing list of emails (one per line)")

	// Enumeration options
	reconCmd.Flags().BoolVar(&findLeakedEmails, "find-emails", false, "Fetch leaked emails from commits in provided and discovered repositories.")
	reconCmd.Flags().BoolVar(&findContributors, "find-contributors", false, "Fetch contributors in provided and discovered repositories.")
	reconCmd.Flags().IntVar(&minContributions, "min-contributions", 0, "Minimum number of contributions required to include a contributor in results.")

	// Output options
	reconCmd.Flags().StringVar(&outputFormat, "output", "json", "Output format: table, json, csv, markdown")

	// Remove mutual exclusion to allow combining different target types
	reconCmd.Flags().SortFlags = false
	reconCmd.PersistentFlags().SortFlags = false
	reconCmd.InheritedFlags().SortFlags = false
}

func runRecon(cmd *cobra.Command) {
	start := time.Now()
	config, _ := cmd.Flags().GetString("config")

	if debug {
		log.Info().Msgf("Running in Debug Mode")
	}
	ctx := context.Background()
	cfg := core.InitConfig(config)

	if cfg.GithubPAT == "" {
		log.Fatal().Msgf("Github Token `github_pat` config value is not set")
	}

	core.InitOctopiDB()

	ghClient, _, err := core.CreateGithubClient(ctx, cfg.GithubPAT)
	if err != nil {
		log.Error().Msgf("%s", err.Error())
	}

	var (
		orgsToEnum   []string
		reposToEnum  []string
		usersToEnum  []string
		emailsToEnum []string
	)

	// Load targets from files
	if fileOrgs, err := core.ReadLinesFromFile(orgsFile); err != nil {
		log.Fatal().Msgf("Error reading orgs file: %s", err)
	} else {
		orgsToEnum = append(orgsToEnum, fileOrgs...)
	}

	if fileRepos, err := core.ReadLinesFromFile(reposFile); err != nil {
		log.Fatal().Msgf("Error reading repos file: %s", err)
	} else {
		reposToEnum = append(reposToEnum, fileRepos...)
	}

	if fileUsers, err := core.ReadLinesFromFile(usersFile); err != nil {
		log.Fatal().Msgf("Error reading users file: %s", err)
	} else {
		usersToEnum = append(usersToEnum, fileUsers...)
	}

	if fileEmails, err := core.ReadLinesFromFile(emailsFile); err != nil {
		log.Fatal().Msgf("Error reading emails file: %s", err)
	} else {
		emailsToEnum = append(emailsToEnum, fileEmails...)
	}

	// Add command-line targets
	orgsToEnum = append(orgsToEnum, orgs...)
	reposToEnum = append(reposToEnum, repos...)
	usersToEnum = append(usersToEnum, users...)
	emailsToEnum = append(emailsToEnum, emails...)

	// Check if we have any targets
	if len(orgsToEnum) == 0 && len(reposToEnum) == 0 && len(usersToEnum) == 0 && len(emailsToEnum) == 0 {
		log.Fatal().Msg("No targets specified. Use --org, --repo, --user, --email flags or corresponding file options.")
	}

	reconSource = &core.Github{
		Client:           ghClient,
		MaxRepoSize:      maxRepoSize * 1024, // Convert MB to KB for internal use
		IncludeForks:     includeForks,
		FindContributors: findContributors,
		FindLeakedEmails: findLeakedEmails,
	}

	var allReports []core.ReconReport

	// Process organizations
	for _, org := range orgsToEnum {
		log.Info().Msgf("Enumerating organization: %s", org)
		orgReconReport, err := reconSource.EnumerateOrganization(ctx, org)
		if err != nil {
			log.Error().Msgf("Failed to enumerate organization %s: %s", org, err.Error())
			continue
		}
		allReports = append(allReports, orgReconReport...)
		err = core.SaveReconReportWithFormat(orgReconReport, outputFormat)
		if err != nil {
			log.Error().Msgf("Failed to save report for organization %s: %s", org, err.Error())
		}
	}

	// Process repositories
	for _, repo := range reposToEnum {
		log.Info().Msgf("Enumerating repository: %s", repo)
		owner, repoName, err := core.ParseGithubRepo(repo)
		if err != nil {
			log.Error().Msgf("Failed to parse repository %s: %s", repo, err.Error())
			continue
		}
		repoReconReport, err := reconSource.EnumerateRepository(ctx, owner, repoName)
		if err != nil {
			log.Error().Msgf("Failed to enumerate repository %s: %s", repo, err.Error())
			continue
		}
		allReports = append(allReports, repoReconReport...)
		err = core.SaveReconReportWithFormat(repoReconReport, outputFormat)
		if err != nil {
			log.Error().Msgf("Failed to save report for repository %s: %s", repo, err.Error())
		}
	}

	// Process users
	for _, user := range usersToEnum {
		log.Info().Msgf("Enumerating user: %s", user)
		userReconReport, err := reconSource.EnumerateUser(ctx, user)
		if err != nil {
			log.Error().Msgf("Failed to enumerate user %s: %s", user, err.Error())
			continue
		}
		allReports = append(allReports, userReconReport...)
		err = core.SaveReconReportWithFormat(userReconReport, outputFormat)
		if err != nil {
			log.Error().Msgf("Failed to save report for user %s: %s", user, err.Error())
		}
	}

	// Process emails
	for _, email := range emailsToEnum {
		log.Info().Msgf("Enumerating email: %s", email)
		emailReconReport, err := reconSource.EnumerateEmail(ctx, email)
		if err != nil {
			log.Error().Msgf("Failed to enumerate email %s: %s", email, err.Error())
			continue
		}
		allReports = append(allReports, emailReconReport...)
		err = core.SaveReconReportWithFormat(emailReconReport, outputFormat)
		if err != nil {
			log.Error().Msgf("Failed to save report for email %s: %s", email, err.Error())
		}
	}

	log.Info().Msgf("Octopi reconnaissance completed in %s", FormatDuration(time.Since(start)))
}
