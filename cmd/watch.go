package cmd

import (
	"context"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/rs/zerolog/log"
	"github.com/spf13/cobra"
	"gitlab.com/hudjefa/octopi/core"
)

var (
	authorName         string
	emailDomain        string
	domainsFile        string
	ignorePrivate      bool
	searchAllCommits   bool
	sensitiveFiles     []string
	sensitivePathsFile string
	alertOnSensitive   bool
	maxFileSizeMB      int
	watchProfile       string
	listProfiles       bool
)

var watchCmd = &cobra.Command{
	Use:   "watch",
	Short: "Watch GitHub commit stream in real-time",
	Long: `Watch observes the GitHub event firehose in real-time,
filtering commits based on email domains or author names.
It can be used to discover repositories that employees of a target
company are committing code to or identify repositories belonging to an individual.`,
	Run: func(cmd *cobra.Command, args []string) {
		runWatch(cmd)
	},
}

func init() {
	watchCmd.Flags().StringVar(&authorName, "author-name", "", "Match author name field (specify multiple with comma)")
	watchCmd.Flags().StringVar(&emailDomain, "email-domain", "", "Match email addresses field (specify multiple with comma)")
	watchCmd.Flags().StringVar(&domainsFile, "domains-file", "", "Match email domains specified in file")
	watchCmd.Flags().BoolVar(&searchAllCommits, "all-commits", false, "Process all commits in each push event (not just the first one)")
	watchCmd.Flags().BoolVar(&ignorePrivate, "ignore-priv", false, "Ignore noreply.github.com private email addresses")

	// Sensitive file monitoring options
	watchCmd.Flags().StringSliceVar(&sensitiveFiles, "sensitive-files", []string{}, "Monitor specific sensitive file patterns (e.g., '*.key,*.pem,config.yaml') - automatically enables file fetching")
	watchCmd.Flags().StringVar(&sensitivePathsFile, "sensitive-paths-file", "", "File containing sensitive file patterns to monitor (one per line) - automatically enables file fetching")
	watchCmd.Flags().BoolVar(&alertOnSensitive, "alert-sensitive", false, "Alert when commits modify sensitive files")
	watchCmd.Flags().IntVar(&maxFileSizeMB, "max-file-size", 50, "Maximum file size to fetch in MB (default: 50MB)")

	// Profile-based configuration options
	watchCmd.Flags().StringVar(&watchProfile, "profile", "", "Use a named watch profile from config.yaml")
	watchCmd.Flags().BoolVar(&listProfiles, "list-profiles", false, "List available watch profiles and exit")

	watchCmd.Flags().SortFlags = false
	watchCmd.PersistentFlags().SortFlags = false
	watchCmd.InheritedFlags().SortFlags = false
}

func runWatch(cmd *cobra.Command) {
	config, _ := cmd.Flags().GetString("config")

	if debug {
		log.Info().Msgf("Running in Debug Mode")
	}

	ctx := context.Background()
	cfg := core.InitConfig(config)

	if cfg.GithubPAT == "" {
		log.Fatal().Msgf("Github Token `github_pat` config value is not set")
	}

	// Handle list profiles command
	if listProfiles {
		listWatchProfiles(cfg)
		return
	}

	// Check notification settings
	if sendSlack && cfg.SlackWebhook == "" {
		log.Fatal().Msgf("Slack webhook URL not configured in config file")
	}

	if sendTelegram && (cfg.TelegramBotId == "" || cfg.TelegramChatId == 0) {
		log.Fatal().Msgf("Telegram bot ID or chat ID not configured in config file")
	}
	if sendDiscord && cfg.DiscordWebhook == "" {
		log.Fatal().Msgf("Discord webhook URL not configured in config file")
	}

	// Initialize database if needed
	core.InitOctopiDB()

	// Create GitHub client
	ghClient, _, err := core.CreateGithubClient(ctx, cfg.GithubPAT)
	if err != nil {
		log.Fatal().Msgf("Failed to create GitHub client: %s", err)
	}

	// Create filter based on profile or command-line arguments
	var filter *core.CommitFilter
	var profile core.WatchProfile
	var useProfile bool

	if watchProfile != "" {
		// Use named profile from config
		if cfg.WatchProfiles == nil {
			log.Fatal().Msgf("No watch profiles configured in config file")
		}

		var exists bool
		profile, exists = cfg.WatchProfiles[watchProfile]
		if !exists {
			log.Fatal().Msgf("Watch profile '%s' not found in config file", watchProfile)
		}

		log.Info().Msgf("Using watch profile: %s - %s", watchProfile, profile.Description)
		filter = core.NewCommitFilterFromProfile(profile)
		useProfile = true
	} else {
		// Use command-line arguments or default profile
		if len(cfg.WatchProfiles) > 0 && authorName == "" && emailDomain == "" && domainsFile == "" && len(sensitiveFiles) == 0 && sensitivePathsFile == "" {
			// No command-line filters specified, use default profile if available
			profile = cfg.DefaultWatch
			if profile.Description != "" {
				log.Info().Msgf("Using default watch profile: %s", profile.Description)
				filter = core.NewCommitFilterFromProfile(profile)
				useProfile = true
			}
		}

		if !useProfile {
			// Use command-line arguments
			cmdMaxFileSizeMB := maxFileSizeMB
			if cmdMaxFileSizeMB == 0 {
				cmdMaxFileSizeMB = 50 // Default to 50MB
			}

			filter = &core.CommitFilter{
				AuthorName:         authorName,
				EmailDomain:        emailDomain,
				DomainsFile:        domainsFile,
				IgnorePrivate:      ignorePrivate,
				SearchAllCommits:   searchAllCommits,
				SensitivePathsFile: sensitivePathsFile,
				AlertOnSensitive:   alertOnSensitive,
				MaxFileSizeMB:      cmdMaxFileSizeMB,
				Enabled:            true,
			}
		}
	}

	// Initialize sensitive files from command line arguments
	if len(sensitiveFiles) > 0 {
		filter.InitializeSensitiveFiles(sensitiveFiles)
	}

	// Load sensitive files from file if specified
	if filter.SensitivePathsFile != "" {
		if err := filter.LoadSensitiveFilesList(); err != nil {
			log.Fatal().Msgf("Failed to load sensitive files list: %s", err)
		}
	}

	// Initialize and load domain list if specified
	if filter.DomainsFile != "" {
		if err := filter.LoadDomainsList(); err != nil {
			log.Fatal().Msgf("Failed to load domains file: %s", err)
		}

		// Start a goroutine to periodically reload the domains file
		go func() {
			ticker := time.NewTicker(30 * time.Second)
			defer ticker.Stop()

			var lastModTime time.Time
			if stat, err := os.Stat(filter.DomainsFile); err == nil {
				lastModTime = stat.ModTime()
			}

			for range ticker.C {
				// Check if file has been modified
				stat, err := os.Stat(filter.DomainsFile)
				if err != nil {
					log.Error().Msgf("Failed to stat domains file: %s", err)
					continue
				}

				if stat.ModTime().After(lastModTime) {
					log.Info().Msgf("Domains file modified, reloading...")
					if err := filter.LoadDomainsList(); err != nil {
						log.Error().Msgf("Failed to reload domains file: %s", err)
					} else {
						log.Info().Msgf("Successfully reloaded domains file with %d domains", len(filter.DomainsList))
						lastModTime = stat.ModTime()
					}
				}
			}
		}()
	}

	// Start the stream
	log.Info().Msg("Starting GitHub commit stream...")

	// Create stats tracker
	stats := &core.StreamStats{}

	// Create commit channel
	commitChan := make(chan []core.CommitEvent, 100)

	// Start processing goroutine
	go processCommits(commitChan, filter, stats, cfg)

	// Determine if we need to fetch files and max file size
	var shouldFetchFiles bool
	var fileSizeLimit int

	if useProfile {
		shouldFetchFiles = profile.ShouldFetchFiles()
		fileSizeLimit = profile.MaxFileSizeMB
	} else {
		shouldFetchFiles = len(sensitiveFiles) > 0 || sensitivePathsFile != ""
		fileSizeLimit = filter.MaxFileSizeMB
	}

	if fileSizeLimit == 0 {
		fileSizeLimit = 50 // Default 50MB
	}

	// Start the GitHub event stream with file size limits
	err = core.StreamGitHubEventsWithFileLimits(ctx, ghClient, filter.SearchAllCommits, shouldFetchFiles, fileSizeLimit, commitChan, stats)
	if err != nil {
		log.Fatal().Msgf("Stream error: %s", err)
	}
}

func processCommits(commitChan <-chan []core.CommitEvent, filter *core.CommitFilter, stats *core.StreamStats, cfg *core.Config) {
	for commits := range commitChan {
		var filteredCommits []core.CommitEvent

		for _, commit := range commits {
			// Check for sensitive files if file details were fetched
			if len(commit.ModifiedFiles) > 0 || len(commit.AddedFiles) > 0 || len(commit.RemovedFiles) > 0 {
				filter.CheckCommitForSensitiveFiles(&commit)
			}

			if filter.Match(commit) {
				filteredCommits = append(filteredCommits, commit)
				stats.IncrementFiltered()
			}

			stats.IncrementProcessed()
		}

		if len(filteredCommits) > 0 {
			handleFilteredCommits(filteredCommits, cfg)
		}
	}
}

func handleFilteredCommits(commits []core.CommitEvent, cfg *core.Config) {
	// Format and print filtered commits with nice output
	for _, c := range commits {
		// Truncate commit message if too long (max 50 chars)
		message := c.Message
		if len(message) > 50 {
			message = message[:47] + "..."
		}

		// Remove newlines from message
		message = strings.ReplaceAll(message, "\n", " ")

		// Format the output with colors and structure
		log.Info().Msgf("%s %s/%s %s",
			GreenColor+c.Timestamp.Format(time.RFC3339)+ResetColor,
			YellowColor+c.UserName+ResetColor,
			YellowColor+c.RepoName+ResetColor,
			message)

		log.Info().Msgf("  Author: %s <%s@%s>  Commit: %s",
			c.AuthorName,
			c.AuthorEmail.User,
			c.AuthorEmail.Domain,
			c.SHA[:7])

		// Show sensitive file alert if applicable
		if c.IsSensitive {
			log.Warn().Msgf("  🚨 SENSITIVE FILES DETECTED in this commit!")
			allFiles := append(c.ModifiedFiles, c.AddedFiles...)
			allFiles = append(allFiles, c.RemovedFiles...)
			if len(allFiles) > 0 {
				log.Info().Msgf("  Files: %s", strings.Join(allFiles, ", "))
			}
		}

		// Add a separator line for readability
		log.Info().Msg("  " + strings.Repeat("-", 50))

		// Send notifications if enabled
		if sendSlack || sendTelegram {
			repoURL := fmt.Sprintf("https://github.com/%s/%s", c.UserName, c.RepoName)

			message := &core.Notification{
				Type:      core.WatchNotification,
				Query:     fmt.Sprintf("Author: %s, Domain: %s", authorName, emailDomain),
				Rule:      "Commit Watch",
				Secret:    "", // No secret for watch notifications
				Commit:    c.SHA,
				Author:    fmt.Sprintf("%s <%s@%s>", c.AuthorName, c.AuthorEmail.User, c.AuthorEmail.Domain),
				File:      "", // No specific file for commit watch
				Line:      0,  // No specific line for commit watch
				Timestamp: c.Timestamp.Format(time.RFC3339),
				RepoURL:   repoURL,
				CommitMsg: c.Message, // Include the commit message
			}

			if sendSlack && cfg.SlackWebhook != "" {
				if err := core.SendSlack(cfg.SlackWebhook, *message); err != nil {
					log.Error().Msgf("Failed to send Slack notification: %s", err)
				} else {
					log.Debug().Msg("Sent commit notification to Slack")
				}
			}

			if sendTelegram && cfg.TelegramBotId != "" && cfg.TelegramChatId != 0 {
				if err := core.SendTelegram(cfg.TelegramBotId, cfg.TelegramChatId, *message); err != nil {
					log.Error().Msgf("Failed to send Telegram notification: %s", err)
				} else {
					log.Debug().Msg("Sent commit notification to Telegram")
				}
			}

			if sendDiscord && cfg.DiscordWebhook != "" {
				if err := core.SendDiscord(cfg.DiscordWebhook, *message); err != nil {
					log.Error().Msgf("Failed to send Discord notification: %s", err)
				} else {
					log.Debug().Msg("Sent commit notification to Discord")
				}
			}
		}
	}
}

// listWatchProfiles displays all available watch profiles
func listWatchProfiles(cfg *core.Config) {
	if len(cfg.WatchProfiles) == 0 {
		log.Info().Msg("No watch profiles configured in config file")
		return
	}

	log.Info().Msg("Available watch profiles:")
	log.Info().Msg(strings.Repeat("=", 50))

	for name, profile := range cfg.WatchProfiles {
		log.Info().Msgf("Profile: %s", name)
		if profile.Description != "" {
			log.Info().Msgf("  Description: %s", profile.Description)
		}

		if len(profile.AuthorNames) > 0 {
			log.Info().Msgf("  Author Names: %s", strings.Join(profile.AuthorNames, ", "))
		}

		if len(profile.EmailDomains) > 0 {
			log.Info().Msgf("  Email Domains: %s", strings.Join(profile.EmailDomains, ", "))
		}

		if profile.DomainsFile != "" {
			log.Info().Msgf("  Domains File: %s", profile.DomainsFile)
		}

		if len(profile.SensitiveFiles) > 0 {
			log.Info().Msgf("  Sensitive Files: %s", strings.Join(profile.SensitiveFiles, ", "))
		}

		if profile.SensitivePathsFile != "" {
			log.Info().Msgf("  Sensitive Paths File: %s", profile.SensitivePathsFile)
		}

		log.Info().Msgf("  Settings: ignore_private=%t, alert_sensitive=%t, fetch_files=%t, max_file_size=%dMB",
			profile.IgnorePrivate, profile.AlertOnSensitive, profile.ShouldFetchFiles(), profile.MaxFileSizeMB)

		notifications := []string{}
		if profile.Notifications.Slack {
			notifications = append(notifications, "Slack")
		}
		if profile.Notifications.Telegram {
			notifications = append(notifications, "Telegram")
		}
		if profile.Notifications.Discord {
			notifications = append(notifications, "Discord")
		}

		if len(notifications) > 0 {
			log.Info().Msgf("  Notifications: %s", strings.Join(notifications, ", "))
		}

		log.Info().Msg(strings.Repeat("-", 30))
	}

	// Show default profile if configured
	if cfg.DefaultWatch.Description != "" {
		log.Info().Msg("Default watch profile:")
		log.Info().Msgf("  Description: %s", cfg.DefaultWatch.Description)
		log.Info().Msgf("  Settings: ignore_private=%t, alert_sensitive=%t, fetch_files=%t, max_file_size=%dMB",
			cfg.DefaultWatch.IgnorePrivate, cfg.DefaultWatch.AlertOnSensitive, cfg.DefaultWatch.ShouldFetchFiles(), cfg.DefaultWatch.MaxFileSizeMB)
	}
}
