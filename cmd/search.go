package cmd

import (
	"bufio"
	"context"
	"os"
	"time"

	"github.com/google/go-github/v72/github"
	"github.com/rs/zerolog/log"
	"github.com/spf13/cobra"
	"gitlab.com/hudjefa/octopi/core"
)

var (
	codeSearch   bool
	commitSearch bool
	userSearch   bool
	webSearch    bool
	queries      []string
	queryFile    string
)

var searchSource = (*core.Github)(nil)

var searchCmd = &cobra.Command{
	Use:   "search",
	Short: "Search Github API and save results to database",
	Run: func(cmd *cobra.Command, args []string) {
		runSearch(cmd)
	},
}

func init() {
	searchCmd.Flags().StringSliceVarP(&queries, "query", "q", nil, "Comma separated list of search queries. You can repeat this flag.")
	searchCmd.Flags().StringVar(&queryFile, "query-file", "", "Path to file containing a list of search queries.")
	searchCmd.Flags().BoolVar(&codeSearch, "code", false, "Search Github code search API endpoint.")
	searchCmd.Flags().BoolVar(&commitSearch, "commit", false, "Search Github commit search API endpoint.")
	searchCmd.Flags().BoolVar(&userSearch, "user", false, "Search Github user search API endpoint.")
	searchCmd.Flags().BoolVar(&webSearch, "web", false, "Search code using web-ui to use regular expressions or wildcards in search qualifiers which are not supported in REST API search.")

	searchCmd.MarkFlagsOneRequired("query", "query-file")
	searchCmd.MarkFlagsOneRequired("code", "commit", "user", "web")
	searchCmd.MarkFlagsMutuallyExclusive("code", "commit", "user", "web")
	searchCmd.Flags().SortFlags = false
	searchCmd.PersistentFlags().SortFlags = false
	searchCmd.InheritedFlags().SortFlags = false
}

func runSearch(cmd *cobra.Command) {
	start := time.Now()
	config, _ := cmd.Flags().GetString("config")

	if debug {
		log.Info().Msgf("Running in Debug Mode")
	}
	ctx := context.Background()
	cfg := core.InitConfig(config)

	if cfg.GithubPAT == "" {
		log.Fatal().Msgf("Github Token `github_pat` config value is not set")
	}

	core.InitOctopiDB()

	ghClient, _, err := core.CreateGithubClient(ctx, cfg.GithubPAT)
	if err != nil {
		log.Error().Msgf("%s", err.Error())
	}

	if queryFile != "" {
		f, err := os.Open(queryFile)
		if err != nil {
			log.Fatal().Msgf("Unable to open repositores list file: %s", err.Error())
		}
		defer f.Close()

		fileScanner := bufio.NewScanner(f)
		fileScanner.Split(bufio.ScanLines)
		for fileScanner.Scan() {
			q := fileScanner.Text()
			queries = append(queries, q)
		}
	}

	switch true {
	case codeSearch:
		StartCodeSearch(ctx, ghClient)
	case commitSearch:
		StartCommitSearch(ctx, ghClient)
	case userSearch:
		StartUserSearch(ctx, ghClient)
	case webSearch:
		StartWebSearch(ctx, ghClient, cfg)
	}
	log.Info().Msgf("Octopi search completed in %s", FormatDuration(time.Since(start)))
}

func StartCodeSearch(ctx context.Context, ghc *github.Client) {
	if len(queries) > 0 {
		log.Info().Msg("Starting Github Code Search")
		for _, query := range queries {
			codeResult, err := core.SearchCode(ctx, ghc, query)
			if err != nil {
				log.Error().Msgf("%s", err.Error())
			}
			if len(codeResult) == 0 {
				continue
			}
			searchSource = &core.Github{
				Client:       ghc,
				MaxRepoSize:  maxRepoSize * 1024, // Convert MB to KB for internal use
				IncludeForks: includeForks,
				CodeResults:  codeResult,
			}
			err = searchSource.ProcessRepos(ctx, query)
			if err != nil {
				log.Error().Msgf("%s", err.Error())
			}
		}
	}
}

func StartCommitSearch(ctx context.Context, ghc *github.Client) {
	if len(queries) > 0 {
		log.Info().Msg("Starting Github Commit Search")
		for _, query := range queries {
			commitResult, err := core.SearchCommits(ctx, ghc, query)
			if err != nil {
				log.Error().Msgf("%s", err.Error())
			}
			if len(commitResult) == 0 {
				continue
			}
			searchSource = &core.Github{
				Client:        ghc,
				MaxRepoSize:   maxRepoSize * 1024, // Convert MB to KB for internal use
				IncludeForks:  includeForks,
				CommitResults: commitResult,
			}
			err = searchSource.ProcessRepos(ctx, query)
			if err != nil {
				log.Error().Msgf("%s", err.Error())
			}
		}
	}
}

func StartUserSearch(ctx context.Context, ghc *github.Client) {
	if len(queries) > 0 {
		log.Info().Msg("Starting Github User Search")
		for _, query := range queries {
			userRepoResult, err := core.SearchUsers(ctx, ghc, query)
			if err != nil {
				log.Error().Msgf("%s", err.Error())
			}
			if len(userRepoResult) == 0 {
				continue
			}
			searchSource = &core.Github{
				Client:       ghc,
				MaxRepoSize:  maxRepoSize * 1024, // Convert MB to KB for internal use
				IncludeForks: includeForks,
				RepoResults:  userRepoResult,
			}
			err = searchSource.ProcessRepos(ctx, query)
			if err != nil {
				log.Error().Msgf("%s", err.Error())
			}
		}
	}
}

func StartWebSearch(ctx context.Context, ghc *github.Client, cfg *core.Config) {
	if len(queries) > 0 {
		log.Info().Msg("Starting Github Code Search (Web-UI)")

		if cfg.GithubUsername == "" || cfg.GithubPassword == "" {
			log.Fatal().Msgf("Github Username or Password config values are not set. You need to provide Github credentials to use Web-UI search. you may also need to set 'github_totp_seed' config value if you have enabled two-factor authentication on your account.")
		}

		ghwc, err := core.LoginToGitHub(core.GitHubCredentials{
			Username: cfg.GithubUsername,
			Password: cfg.GithubPassword,
			OTP:      cfg.GithubTOTPSeed,
		})
		if err != nil {
			log.Error().Msgf("%s", err.Error())
		}

		g := &core.Github{
			HTTPClient:   ghwc,
			MaxRepoSize:  maxRepoSize * 1024,
			IncludeForks: includeForks,
		}

		for _, query := range queries {
			codeResult, err := g.SearchCodeWithUI(ctx, query)
			if err != nil {
				log.Error().Msgf("%s", err.Error())
			}
			if len(codeResult) == 0 {
				continue
			}
			g.CodeResults = codeResult
			searchSource = &core.Github{
				Client:       ghc,
				MaxRepoSize:  maxRepoSize * 1024,
				IncludeForks: includeForks,
				CodeResults:  codeResult,
			}
			err = searchSource.ProcessRepos(ctx, query)
			if err != nil {
				log.Error().Msgf("%s", err.Error())
			}
		}
	}
}
