package cmd

import (
	"os"
	"strings"
	"time"

	"github.com/rs/zerolog/log"
	"github.com/spf13/cobra"
	"gitlab.com/hudjefa/octopi/core"
)

var (
	debug        bool
	nocolor      bool
	includeForks bool
	maxRepoSize  int
	outputDir    string
	sendSlack    bool
	sendTelegram bool
	sendDiscord  bool
	bannerShown  bool // Track if banner was already shown in error handling
)

const (
	GreenColor  string = "\033[32m"
	YellowColor string = "\033[33m"
	RedColor    string = "\033[31m"
	ResetColor  string = "\033[0m"
	LimeColor   string = "\033[1;32m"
)

func Banner(useColor bool) string {
	var banner string

	if useColor {
		banner = GreenColor
	}

	banner += `
╔═╗╔═╗╔╦╗╔═╗╔═╗╦
║ ║║   ║ ║ ║╠═╝║
╚═╝╚═╝ ╩ ╚═╝╩  ╩
`

	if useColor {
		banner += ResetColor + "Octopi version: " + GreenColor + core.Version + ResetColor + "\n\n"
	} else {
		banner += "Octopi version: " + core.Version + "\n\n"
	}

	return banner
}

const (
	GitBinary        string = "git"
	GitleaksBinary   string = "gitleaks"
	TrufflehogBinary string = "trufflehog"
)

var (
	rootCmd = &cobra.Command{
		Use:     "octopi",
		Version: core.Version,
		Short:   "Github secrets leakage inspector",
		Long: `Octopi uses Github search API to find and collect repositories
based on a configured list of search queries then scan them for
exposed secrets using tools like Gitleaks and Dangler.
`,
		Run: func(cmd *cobra.Command, args []string) {
			// Only show help if no arguments are provided
			// If arguments are provided, they might be subcommands that will be handled separately
			if len(args) == 0 {
				cmd.Help()
			}
		},
		PersistentPreRun: func(cmd *cobra.Command, args []string) {
			// Check both --no-color flag and NO_COLOR environment variable for logging
			noColorFinal := nocolor || os.Getenv("NO_COLOR") != ""

			// Reconfigure logging with user-specified debug and nocolor settings
			core.SetupLogging(debug, noColorFinal)

			// Only show banner once per execution
			if !bannerShown {
				// Check both --no-color flag and NO_COLOR environment variable
				shouldUseColor := !noColorFinal
				println(Banner(shouldUseColor))
				bannerShown = true
			}
		},
	}
)

func init() {
	// Set up basic logging early to ensure consistent error formatting
	// Check NO_COLOR environment variable (industry standard) and do a quick flag check
	noColorEarly := os.Getenv("NO_COLOR") != "" || checkNoColorFlag()
	core.SetupLogging(false, noColorEarly)

	cobra.EnableCommandSorting = false
	rootCmd.Flags().SortFlags = false
	rootCmd.PersistentFlags().SortFlags = false
	rootCmd.CompletionOptions.DisableDefaultCmd = true

	rootCmd.AddCommand(reconCmd)
	rootCmd.AddCommand(searchCmd)
	rootCmd.AddCommand(scanCmd)
	rootCmd.AddCommand(watchCmd)

	rootCmd.PersistentFlags().BoolVarP(&debug, "debug", "d", false, "Run Octopi in debug mode.")
	rootCmd.PersistentFlags().BoolVarP(&nocolor, "no-color", "n", false, "Disable colors in output")
	rootCmd.PersistentFlags().StringP("config", "c", "", "Path to octopi config file (default is ~/.octopi/config.yaml)")
	rootCmd.PersistentFlags().StringVarP(&outputDir, "output-dir", "O", "", "Path to directory for saving JSON format results.")
	rootCmd.PersistentFlags().BoolVarP(&includeForks, "include-forks", "f", false, "Include repository forks in recon, search and scan.")
	rootCmd.PersistentFlags().IntVarP(&maxRepoSize, "max-size", "m", 0, "Maximum repository size in MB for recon, search and scan, unlimited by default.")
	rootCmd.PersistentFlags().BoolVar(&sendSlack, "slack", false, "Send notifications to Slack via webhook.")
	rootCmd.PersistentFlags().BoolVar(&sendTelegram, "telegram", false, "Send notifications to Telegram via bot.")
	rootCmd.PersistentFlags().BoolVar(&sendDiscord, "discord", false, "Send notifications to Discord via webhook.")

	rootCmd.SetVersionTemplate("{{printf \"v%s\" .Version}}\n")

	// Remove custom help function for now to debug double banner issue
	// rootCmd.SetHelpFunc(...)

	// Set custom error handling for flag parsing errors
	rootCmd.SilenceErrors = true // We'll handle errors ourselves
	rootCmd.SilenceUsage = true  // Don't show usage on errors automatically

	defaultUsageTemplate := rootCmd.UsageTemplate()
	rootCmd.SetUsageTemplate(defaultUsageTemplate + "\nCopyright © 2024 Hologram <<EMAIL>>\n" +
		"Distributed under the Apache License Version 2.0 (Apache-2.0)\n" +
		"For further details, visit https://github.com/hologram/octopi\n")
}

func FormatDuration(d time.Duration) string {
	scale := 100 * time.Second
	for scale > d {
		scale = scale / 10
	}
	return d.Round(scale / 100).String()
}

func Execute() {
	// Custom error handling to ensure proper logging and banner display
	if err := rootCmd.Execute(); err != nil {
		// Only show banner if PersistentPreRun didn't run (true flag parsing errors)
		// For validation errors, PersistentPreRun already ran and showed the banner
		if !bannerShown {
			noColorFromArgs := os.Getenv("NO_COLOR") != "" || checkNoColorFlag()
			println(Banner(!noColorFromArgs))
			bannerShown = true
		}

		// Show the error
		log.Error().Msgf("%s", err.Error())

		// Show usage information to help the user
		cmd := findFailedCommand()
		if cmd != nil {
			println("\n" + cmd.UsageString())
		} else {
			println("\n" + rootCmd.UsageString())
		}

		os.Exit(1)
	}
}

// findFailedCommand attempts to identify which command failed based on os.Args
func findFailedCommand() *cobra.Command {
	if len(os.Args) < 2 {
		return rootCmd
	}

	// Check if the second argument matches a known command
	cmdName := os.Args[1]
	for _, cmd := range rootCmd.Commands() {
		if cmd.Name() == cmdName {
			return cmd
		}
	}

	return rootCmd
}

// checkNoColorFlag quickly checks if --no-color flag is present in command line
func checkNoColorFlag() bool {
	for _, arg := range os.Args {
		if arg == "--no-color" || arg == "-n" {
			return true
		}
		// Handle combined short flags like -nd
		if len(arg) > 1 && arg[0] == '-' && arg[1] != '-' {
			if strings.Contains(arg, "n") {
				return true
			}
		}
	}
	return false
}
