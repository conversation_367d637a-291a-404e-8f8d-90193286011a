# Octopi Configuration Example with Watch Profiles
github_pat: "your_github_token_here"
slack_webhook: "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK"
telegram_botid: "your_telegram_bot_id"
telegram_chatid: *********
discord_webhook: "https://discord.com/api/webhooks/YOUR/DISCORD/WEBHOOK"

# Multiple named watch profiles for different monitoring scenarios
watch_profiles:
  security_monitoring:
    description: "Monitor for security-related commits and sensitive file changes"
    author_names:
      - "security-team"
      - "devops"
      - "admin"
    email_domains:
      - "company.com"
      - "contractor.com"
    sensitive_files:
      # Sensitive file patterns (supports wildcards)
      - "*.key"
      - "*.pem"
      - "*.p12"
      - "*.env"
      - "config.yaml"
      - "secrets/*"
      - "credentials/*"
      - ".aws/credentials"
      - "database.yml"
      # File extensions can be monitored using wildcards
      - "*.py"     # Monitor all Python files
      - "*.js"     # Monitor all JavaScript files
      - "*.go"     # Monitor all Go files
      - "*.yaml"   # Monitor all YAML files
      - "*.yml"    # Monitor all YML files
      - "*.json"   # Monitor all JSON files
      - "*.sh"     # Monitor all shell scripts
    alert_on_sensitive: true
    max_file_size_mb: 25  # Limit to 25MB for security monitoring
    ignore_private: true
    search_all_commits: true
    repo_filters:
      new_repositories: 30    # Exclude repos created in last 30 days
      forked_repos: false     # Exclude forked repositories
      archived_repos: false   # Exclude archived repositories
      repo_size_limit: 1000   # Exclude repos larger than 1GB
      star_count_min: 10      # Only monitor repos with 10+ stars
    notifications:
      slack: true
      telegram: true
      discord: false

  contractor_monitoring:
    description: "Monitor contractor commits to sensitive areas"
    email_domains:
      - "contractor1.com"
      - "contractor2.com"
      - "freelancer.com"
    sensitive_files:
      - "src/core/*"
      - "database/*"
      - "*.sql"
      - "migrations/*"
      - "config/*"
    alert_on_sensitive: true
    max_file_size_mb: 10  # Smaller limit for contractor monitoring
    ignore_private: false
    notifications:
      slack: true
      telegram: false

  executive_monitoring:
    description: "Monitor executive team repositories for any activity"
    author_names:
      - "ceo"
      - "cto"
      - "ciso"
      - "founder"
    ignore_private: false
    alert_on_sensitive: false
    search_all_commits: true
    notifications:
      slack: true
      telegram: true

  compliance_monitoring:
    description: "Monitor for compliance-related file changes"
    sensitive_files:
      - "*.policy"
      - "compliance/*"
      - "audit/*"
      - "gdpr/*"
      - "privacy/*"
      - "terms-of-service.*"
      - "privacy-policy.*"
    alert_on_sensitive: true
    fetch_files: true
    ignore_private: true
    notifications:
      slack: true

  infrastructure_monitoring:
    description: "Monitor infrastructure and deployment changes"
    sensitive_files:
      - "Dockerfile"
      - "docker-compose.yml"
      - "kubernetes/*"
      - "terraform/*"
      - "ansible/*"
      - "*.tf"      # Terraform files
      - "*.tfvars"  # Terraform variables
      - "*.yml"     # YAML files
      - "*.yaml"    # YAML files
      - "*.json"    # JSON files
      - "*.sh"      # Shell scripts
      - "*.py"      # Python scripts
      - "helm/*"
      - "charts/*"
    alert_on_sensitive: true
    ignore_private: true
    repo_filters:
      forked_repos: true      # Include forked repos for infrastructure
      archived_repos: false   # Exclude archived repos
      repo_size_limit: 2000   # Allow larger infrastructure repos
      star_count_min: 5       # Lower star requirement
    notifications:
      slack: true
      discord: true

  domain_monitoring:
    description: "Monitor commits from specific email domains"
    domains_file: "monitored-domains.txt"
    ignore_private: true
    notifications:
      slack: true

# Default watch settings used when no profile is specified
default_watch:
  description: "Default monitoring for general security awareness"
  ignore_private: true
  alert_on_sensitive: false
  search_all_commits: false
  notifications:
    slack: false
    telegram: false
    discord: false

# Standard Octopi configuration
ignore_users:
  - "dependabot"
  - "renovate"
  - "github-actions"

ignore_repos:
  - "company/test-repo"
  - "company/archived-project"
