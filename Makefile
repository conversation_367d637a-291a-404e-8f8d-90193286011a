SHELL = /bin/bash

.PHONY: all build clean help install package lint
.DEFAULT_GOAL := help

CURRENT_DIR := $(shell dirname $(realpath $(lastword $(MAKEFILE_LIST))))
BUILD_TIME := $(shell date -u '+%F_%T')
VERSION ?= $(shell (git describe --tags --dirty --match='v*' 2>/dev/null || echo v1.0.0) | cut -c2-)

ifndef pkg
pkg := $(shell pwd | awk -F/ '{print tolower($$NF)}')
endif

ifndef target_os
	UNAME_S := $(shell uname -s)
	ifeq ($(UNAME_S),Linux)
		target_os = linux
	endif
	ifeq ($(UNAME_S),Darwin)
		target_os = darwin
	endif
	UNAME_P := $(shell uname -p)

	ifeq ($(UNAME_P),x86_64)
		target_arch = amd64
	endif
endif

ifeq ($(target_os),windows)
	target_ext = .exe
endif

ifndef target_arch
	target_arch = amd64
endif

## all		Run lint tools, clean and build
all: lint clean build

## build		Download dependencies and build
build: prep
	@GOOS=$(target_os) GOARCH=$(target_arch) go build -o $(CURRENT_DIR)/$(pkg)$(target_ext)

## release		Download dependencies and build release binaries
release: prep
	@GOOS=linux GOARCH=$(target_arch) go build -ldflags="-s -w" -o $(CURRENT_DIR)/bin/$(pkg)-linux
	@GOOS=darwin GOARCH=$(target_arch) go build -ldflags="-s -w" -o $(CURRENT_DIR)/bin/$(pkg)-darwin
	@GOOS=windows GOARCH=$(target_arch) go build -ldflags="-s -w" -o $(CURRENT_DIR)/bin/$(pkg)-windows.exe

## clean		Clean binaries
clean:
	@rm -rf $(CURRENT_DIR)/$(pkg) $(CURRENT_DIR)/bin

## help		Print available make targets
help:
	@echo
	@echo "Available make targets:"
	@sed -ne '/@sed/!s/## /	/p' $(MAKEFILE_LIST)

## install		Build and save binary in `$GOPATH/bin/`
install:
	@GOOS=$(target_os) GOARCH=$(target_arch) go install

## package		Run tests, clean and build binary
package: clean build

## prep		Install dependencies
prep:
	@go get

## lint		Run golint, go fmt and go vet
lint:
	@echo "-> $@"
	@gofmt -s -l ./ | grep -v vendor | tee /dev/stderr
	@golint ./... | tee /dev/stderr
	@go vet --all

## test		Run tests with coverage
test: pretty
    go test ./... -cover
