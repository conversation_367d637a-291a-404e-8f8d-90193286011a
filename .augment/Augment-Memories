# Code Review Preferences
- The user wants to be consulted before any actions are taken when reviewing code for bugs or issues.
- The user wants all identified bugs and issues to be addressed, including issue 5 about inconsistent repository URL handling.

# Dangler Scan Command Options
- The user wants to introduce dangler.options{} as options in the scan command with specific flags for controlling different types of commit detection.
- MaxBranches and DefaultBranchOnly options have been removed from dangler.
- The user wants the scan command to support on-demand manual GitHub repository scanning without requiring repositories to be pre-loaded/saved by the search command in the database.
- When manual targets are specified for scan command, skipDatabase should be part of dangler.options and enabled by default, following the same pattern as other dangler options.
- Dangler and bruter should remain mutually exclusive.
- <PERSON>ruter should only be used on-demand with manual targets (--repo), no --bruter-only flag needed.
- <PERSON><PERSON><PERSON> should only fetch dangling commits with FetchDanglingCommit(), not all valid commits discovered during brute force scanning.
- Invalid commits cache should be preserved for resuming scans.
- The TruffleHog reference implementation in core/bruter should be closely followed to avoid implementation errors.

# Watch/Stream Feature Enhancement
- User wants to add file name/path-based filtering and alerting capabilities to the watch/stream feature for detecting commits that create or update sensitive files.
- User prefers using wildcard patterns in sensitive_files over separate file_extensions filtering since it achieves the same goal with existing functionality.
- When sensitive_files is configured, fetch_files should be automatically enabled and not be an optional setting since sensitive file monitoring requires file path information. The user prefers simple automatic behavior: when sensitive_files are configured, automatically do what's needed without complex enable/disable options or checks.
- User wants to add a maximum file size limit for fetching files in the watch/stream feature with a sensible default to prevent excessive API usage.
- User wants repo filtering (new repos 30 days, no forks/archived by default, size limit 1GB, min 10 stars) and not security regex content parsing for the watch tool.

# Recon Command Improvements
- User wants recon command improvements: output formats (#2), --min-contributions flag, and file input flags (--orgs-file, --emails-file, --repos-file, --users-file).
- User wants to integrate multiple output format options (json, csv, markdown, table) into SaveReconReport function with json as default, similar to the recon command implementation.
- User prefers using existing Unique() function instead of custom removeDuplicates() implementations when available.

# Git Command Execution
- User prefers to move Git command execution functions to git.go and use the same method pattern that's already established there for running Git commands.
