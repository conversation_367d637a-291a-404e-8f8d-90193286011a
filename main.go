package main

import (
	"os"
	"os/signal"
	"path/filepath"

	"github.com/rs/zerolog/log"
	"gitlab.com/hudjefa/octopi/cmd"
)

func main() {
	stopChan := make(chan os.Signal, 1)
	signal.Notify(stop<PERSON><PERSON>, os.Interrupt)
	go listenForInterrupt(stopChan)

	cmd.Execute()
}

func listenForInterrupt(stopScan chan os.Signal) {
	<-stopScan
	_ = os.RemoveAll(filepath.Join(os.TempDir(), "octopi"))
	log.Fatal().Msg("Interrupt signal received. Exiting...")
}
